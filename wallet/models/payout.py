from sqlalchemy import Column, String, DateTime, ForeignKey, Numeric, JSON, func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from db import Base
import uuid


class Payout(Base):
    __tablename__ = "payouts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    provider = Column(String, nullable=False, default="cashfree")
    link_id = Column(String, nullable=False)  # Cashgram ID or similar
    status = Column(String, nullable=False, default="created")  # created, success, failed, expired
    amount = Column(Numeric(14, 2), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    raw_webhook = Column(JSON, nullable=True)  # Store raw webhook payload
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="payouts")
    
    def __repr__(self):
        return f"<Payout(id={self.id}, link_id={self.link_id}, status={self.status}, amount={self.amount})>"
