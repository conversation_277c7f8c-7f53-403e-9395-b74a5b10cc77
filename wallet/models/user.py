from sqlalchemy import Column, String, DateTime, func
from sqlalchemy.orm import relationship
from db import Base


class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True)  # Razorpay customer_id
    name = Column(String, nullable=True)  # Customer name from Razorpay
    kyc_status = Column(String, nullable=False, default="pending")  # pending, verified, rejected
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    ledger_accounts = relationship("LedgerAccount", back_populates="user")
    payments = relationship("Payment", back_populates="user")
    payouts = relationship("Payout", back_populates="user")
    
    def __repr__(self):
        return f"<User(id={self.id}, name={self.name}, kyc_status={self.kyc_status})>"
