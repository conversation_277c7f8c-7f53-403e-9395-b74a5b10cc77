from sqlalchemy import Column, String, DateTime, ForeignKey, Numeric, JSON, func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from db import Base
import uuid


class Payment(Base):
    __tablename__ = "payments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    gateway = Column(String, nullable=False, default="razorpay")
    order_id = Column(String, nullable=False)
    payment_id = Column(String, nullable=True)  # Set when payment is captured
    status = Column(String, nullable=False, default="created")  # created, captured, failed
    amount = Column(Numeric(14, 2), nullable=False)
    fee = Column(Numeric(14, 2), nullable=False, default=0)
    tax = Column(Numeric(14, 2), nullable=False, default=0)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    raw_webhook = Column(JSON, nullable=True)  # Store raw webhook payload
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="payments")
    
    def __repr__(self):
        return f"<Payment(id={self.id}, order_id={self.order_id}, status={self.status}, amount={self.amount})>"
