from sqlalchemy import Column, String, DateTime, ForeignKey, Numeric, JSON, UniqueConstraint, func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from db import Base
import uuid


class LedgerAccount(Base):
    __tablename__ = "ledger_accounts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String, ForeignKey("users.id"), nullable=True)  # null for platform accounts
    type = Column(String, nullable=False)  # user_balance, platform_cash, fees, escrow_receivable
    currency = Column(String, nullable=False, default="INR")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="ledger_accounts")
    journal_entries = relationship("JournalEntry", back_populates="account")
    balance_snapshots = relationship("BalanceSnapshot", back_populates="account")
    
    __table_args__ = (
        UniqueConstraint('user_id', 'type', 'currency', name='unique_user_account_type'),
    )
    
    def __repr__(self):
        return f"<LedgerAccount(id={self.id}, type={self.type}, user_id={self.user_id})>"


class JournalEntry(Base):
    __tablename__ = "journal_entries"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    entry_id = Column(UUID(as_uuid=True), nullable=False)  # Groups related entries
    account_id = Column(UUID(as_uuid=True), ForeignKey("ledger_accounts.id"), nullable=False)
    debit = Column(Numeric(14, 2), nullable=False, default=0)
    credit = Column(Numeric(14, 2), nullable=False, default=0)
    currency = Column(String, nullable=False, default="INR")
    ref_type = Column(String, nullable=False)  # payment, payout, adjustment
    ref_id = Column(String, nullable=False)  # Reference to external entity
    idempotency_key = Column(String, nullable=False, unique=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    account = relationship("LedgerAccount", back_populates="journal_entries")
    
    def __repr__(self):
        return f"<JournalEntry(id={self.id}, debit={self.debit}, credit={self.credit})>"


class BalanceSnapshot(Base):
    __tablename__ = "balance_snapshots"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    account_id = Column(UUID(as_uuid=True), ForeignKey("ledger_accounts.id"), nullable=False, unique=True)
    balance = Column(Numeric(14, 2), nullable=False, default=0)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    account = relationship("LedgerAccount", back_populates="balance_snapshots")
    
    def __repr__(self):
        return f"<BalanceSnapshot(account_id={self.account_id}, balance={self.balance})>"
