# Wallet Service

A production-ready wallet microservice built with FastAPI, featuring Razorpay & Cashfree integration, double-entry ledger accounting, and a minimal admin UI.

## Features

- **Double-Entry Ledger**: Append-only journal with idempotency guarantees
- **Payment Integration**: Razorpay Orders, Checkout, and Virtual Accounts
- **Payout Integration**: Cashfree Cashgram for withdrawals
- **Admin Dashboard**: Minimal UI for monitoring users, balances, and transactions
- **Webhook Security**: HMAC signature verification for all webhooks
- **ORM-Only**: No raw SQL - uses SQLAlchemy ORM and Alembic migrations
- **Async Architecture**: Built with FastAPI and async/await patterns
- **Docker Ready**: Complete containerization with docker-compose

## Tech Stack

- **Python 3.11+**
- **FastAPI** (async web framework)
- **SQLAlchemy 2.x** (ORM only)
- **Alembic** (database migrations)
- **PostgreSQL** (primary) / SQLite (development)
- **httpx** (async HTTP client)
- **Pydantic v2** (data validation)
- **Jinja2 + Tailwind CSS** (admin UI)

## Quick Start

### Using Docker (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <repository>
   cd wallet-service
   cp .env.example .env
   ```

2. **Configure environment** (edit `.env`):
   ```bash
   # Required for production
   RAZORPAY_KEY_ID=rzp_live_xxx
   RAZORPAY_KEY_SECRET=xxxxx
   RAZORPAY_WEBHOOK_SECRET=whsec_xxx
   
   CASHFREE_CLIENT_ID=cf_xxx
   CASHFREE_CLIENT_SECRET=xxxxx
   CASHFREE_WEBHOOK_SECRET=whsec_xxx
   
   ADMIN_API_KEY=your_secure_admin_key
   ```

3. **Start services**:
   ```bash
   make docker-dev
   ```

4. **Access the application**:
   - API: http://localhost:8000
   - Admin UI: http://localhost:8000/admin
   - API Docs: http://localhost:8000/docs

### Local Development

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Setup database**:
   ```bash
   make migrate
   make seed
   ```

3. **Run development server**:
   ```bash
   make dev
   ```

## API Endpoints

### Public Endpoints

#### Create Topup Order
```bash
curl -X POST http://localhost:8000/api/topups/order \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "alice_123",
    "amount": 100.00,
    "receipt": "topup_001"
  }'
```

#### Create Virtual Account
```bash
curl -X POST http://localhost:8000/api/topups/virtual-account \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "alice_123"
  }'
```

#### Create Withdrawal
```bash
curl -X POST http://localhost:8000/api/withdrawals \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "alice_123",
    "amount": 50.00,
    "phone": "**********",
    "email": "<EMAIL>"
  }'
```

### Webhook Endpoints

#### Razorpay Webhook
```bash
curl -X POST http://localhost:8000/webhooks/razorpay \
  -H "Content-Type: application/json" \
  -H "X-Razorpay-Signature: signature_here" \
  -d '{
    "event": "payment.captured",
    "payload": {
      "payment": {
        "entity": {
          "id": "pay_xxx",
          "order_id": "order_xxx",
          "amount": 10000,
          "fee": 236,
          "status": "captured",
          "captured": true
        }
      }
    }
  }'
```

#### Cashfree Webhook
```bash
curl -X POST http://localhost:8000/webhooks/cashfree \
  -H "Content-Type: application/json" \
  -H "x-webhook-signature: signature_here" \
  -H "x-webhook-timestamp: timestamp_here" \
  -d '{
    "type": "CASHGRAM_SUCCESS",
    "data": {
      "cashgramId": "cg_xxx",
      "status": "SUCCESS",
      "amount": 50.00
    }
  }'
```

### Admin Endpoints

All admin endpoints require the `X-API-Key` header:

```bash
curl -X GET http://localhost:8000/admin/api/dashboard-stats \
  -H "X-API-Key: supersecretadminkey"
```

## Admin UI

Access the admin dashboard at http://localhost:8000/admin with the API key header.

### Features:
- **Dashboard**: KPI cards showing wallet float, user count, transaction volumes
- **Users**: List all users with balances and search functionality
- **Balances**: Platform balance overview and user balance details
- **Journal**: Paginated ledger entries with filtering
- **Payments**: Transaction history with status filtering
- **Payouts**: Withdrawal history with status filtering

## Database Schema

### Core Tables

- **users**: User accounts with KYC status
- **ledger_accounts**: Chart of accounts (user balances, platform accounts)
- **journal_entries**: Double-entry ledger with idempotency keys
- **payments**: Razorpay payment records
- **payouts**: Cashfree payout records
- **balance_snapshots**: Materialized balance cache (optional)

### Account Types

- `user_balance`: Individual user wallet balances
- `platform_cash`: Platform's cash position
- `fees`: Collected transaction fees
- `escrow_receivable`: Pending settlements from payment gateways

## Double-Entry Accounting

Every transaction creates balanced journal entries:

### Payment Flow (User Topup)
```
1. Settlement:    escrow_receivable → platform_cash
2. Fee Collection: platform_cash → fees  
3. User Credit:   platform_cash → user_balance
```

### Payout Flow (User Withdrawal)
```
1. Withdrawal:    user_balance → platform_cash
```

## Idempotency

All external events (webhooks, API calls) are processed idempotently using:
- Unique idempotency keys per transaction
- Database constraints to prevent duplicates
- Service-level checks for replay protection

## Testing

Run the test suite:

```bash
make test
```

### Test Coverage
- **Ledger Service**: Double-entry validation, balance calculations, idempotency
- **Webhook Processing**: Payment capture, payout success, signature verification
- **Edge Cases**: Insufficient balance, duplicate webhooks, invalid signatures

## Deployment

### Production Checklist

1. **Environment Variables**:
   - Set real Razorpay/Cashfree credentials
   - Use strong `ADMIN_API_KEY`
   - Configure `SERVICE_IDEMPOTENCY_SALT`

2. **Database**:
   - Use PostgreSQL in production
   - Run migrations: `make migrate`

3. **Security**:
   - Enable webhook signature verification
   - Use HTTPS for all endpoints
   - Restrict admin UI access

4. **Monitoring**:
   - Set up logging aggregation
   - Monitor webhook delivery failures
   - Alert on balance discrepancies

### Docker Production

```bash
# Build production image
docker build -t wallet-service:latest .

# Run with production config
docker run -d \
  --name wallet-service \
  -p 8000:8000 \
  -e DB_URL=postgresql+asyncpg://user:pass@db:5432/wallet \
  -e RAZORPAY_KEY_ID=rzp_live_xxx \
  -e RAZORPAY_KEY_SECRET=xxx \
  wallet-service:latest
```

## Development Commands

```bash
# Development
make dev          # Start development server
make migrate      # Run database migrations  
make seed         # Seed demo data
make test         # Run tests

# Docker
make build        # Build Docker image
make up           # Start with docker-compose
make down         # Stop services
make logs         # View logs
make clean        # Clean up containers

# Utilities
make format       # Format code with black
make lint         # Run linting
```

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Razorpay      │    │   Wallet Service │    │   Cashfree      │
│   (Payments)    │───▶│                  │◀───│   (Payouts)     │
└─────────────────┘    │                  │    └─────────────────┘
                       │  ┌─────────────┐ │
                       │  │   Ledger    │ │
                       │  │   Service   │ │
                       │  └─────────────┘ │
                       │  ┌─────────────┐ │
                       │  │  Admin UI   │ │
                       │  └─────────────┘ │
                       └──────────────────┘
                              │
                       ┌──────▼──────┐
                       │ PostgreSQL  │
                       │ Database    │
                       └─────────────┘
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Check the test files for usage examples
- Review the API documentation at `/docs`
- Examine the admin UI for data visualization

---

**Note**: This service is designed for closed-loop wallet systems. Ensure compliance with local financial regulations before production deployment.
