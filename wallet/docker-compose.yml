version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: wallet
      POSTGRES_USER: wallet_user
      POSTGRES_PASSWORD: wallet_pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U wallet_user -d wallet"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - DB_URL=postgresql+asyncpg://wallet_user:wallet_pass@db:5432/wallet
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - .:/app
    command: >
      sh -c "
        python3 -m alembic upgrade head &&
        uvicorn app:app --host 0.0.0.0 --port 8000 --reload
      "
    stdin_open: true
    tty: true

volumes:
  postgres_data:
