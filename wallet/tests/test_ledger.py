import pytest
import asyncio
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select

from db import Base
from models import User, LedgerAccount, JournalEntry
from services.ledger_service import LedgerService, Line


# Test database setup
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_wallet.db"

@pytest.fixture
async def db_session():
    """Create a test database session"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    AsyncSessionLocal = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with AsyncSessionLocal() as session:
        yield session
    
    # Cleanup
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest.fixture
async def test_user(db_session):
    """Create a test user"""
    user = User(id="test_user_123", kyc_status="verified")
    db_session.add(user)
    await db_session.commit()
    return user


@pytest.mark.asyncio
async def test_create_core_accounts(db_session):
    """Test creation of core platform accounts"""
    ledger_service = LedgerService(db_session)
    
    core_accounts = await ledger_service.get_or_create_core_accounts()
    
    assert "platform_cash" in core_accounts
    assert "fees" in core_accounts
    assert "escrow_receivable" in core_accounts
    
    # Verify accounts are created in database
    for account_type, account in core_accounts.items():
        assert account.type == account_type
        assert account.user_id is None
        assert account.currency == "INR"


@pytest.mark.asyncio
async def test_create_user_balance_account(db_session, test_user):
    """Test creation of user balance account"""
    ledger_service = LedgerService(db_session)
    
    account = await ledger_service.get_or_create_user_balance_account(test_user.id)
    
    assert account.user_id == test_user.id
    assert account.type == "user_balance"
    assert account.currency == "INR"


@pytest.mark.asyncio
async def test_double_entry_posting(db_session, test_user):
    """Test double-entry journal posting"""
    ledger_service = LedgerService(db_session)
    
    # Create accounts
    core_accounts = await ledger_service.get_or_create_core_accounts()
    user_account = await ledger_service.get_or_create_user_balance_account(test_user.id)
    
    # Create balanced journal entries
    amount = Decimal("100.00")
    lines = [
        Line(account_id=str(core_accounts["platform_cash"].id), credit=amount),
        Line(account_id=str(user_account.id), debit=amount)
    ]
    
    entries = await ledger_service.post_double_entry(
        lines=lines,
        ref_type="test",
        ref_id="test_txn_123"
    )
    
    assert len(entries) == 2
    
    # Verify balances
    user_balance = await ledger_service.get_user_balance(test_user.id)
    assert user_balance == amount
    
    platform_balance = await ledger_service.get_account_balance(str(core_accounts["platform_cash"].id))
    assert platform_balance == -amount  # Credit balance


@pytest.mark.asyncio
async def test_unbalanced_entry_rejection(db_session, test_user):
    """Test that unbalanced entries are rejected"""
    ledger_service = LedgerService(db_session)
    
    core_accounts = await ledger_service.get_or_create_core_accounts()
    user_account = await ledger_service.get_or_create_user_balance_account(test_user.id)
    
    # Create unbalanced entries
    lines = [
        Line(account_id=str(core_accounts["platform_cash"].id), credit=Decimal("100.00")),
        Line(account_id=str(user_account.id), debit=Decimal("50.00"))  # Unbalanced!
    ]
    
    with pytest.raises(ValueError, match="Unbalanced entry"):
        await ledger_service.post_double_entry(
            lines=lines,
            ref_type="test",
            ref_id="test_txn_456"
        )


@pytest.mark.asyncio
async def test_idempotency(db_session, test_user):
    """Test idempotency of journal entries"""
    ledger_service = LedgerService(db_session)
    
    core_accounts = await ledger_service.get_or_create_core_accounts()
    user_account = await ledger_service.get_or_create_user_balance_account(test_user.id)
    
    amount = Decimal("100.00")
    lines = [
        Line(account_id=str(core_accounts["platform_cash"].id), credit=amount),
        Line(account_id=str(user_account.id), debit=amount)
    ]
    
    # Post the same transaction twice
    entries1 = await ledger_service.post_double_entry(
        lines=lines,
        ref_type="test",
        ref_id="idempotent_test"
    )
    
    entries2 = await ledger_service.post_double_entry(
        lines=lines,
        ref_type="test",
        ref_id="idempotent_test"
    )
    
    # Should return the same entries
    assert len(entries1) == len(entries2) == 2
    assert entries1[0].id == entries2[0].id
    assert entries1[1].id == entries2[1].id
    
    # Balance should not be doubled
    user_balance = await ledger_service.get_user_balance(test_user.id)
    assert user_balance == amount


@pytest.mark.asyncio
async def test_wallet_float_calculation(db_session):
    """Test total wallet float calculation"""
    ledger_service = LedgerService(db_session)
    
    # Create multiple users with balances
    users = []
    for i in range(3):
        user = User(id=f"user_{i}", kyc_status="verified")
        db_session.add(user)
        users.append(user)
    
    await db_session.commit()
    
    core_accounts = await ledger_service.get_or_create_core_accounts()
    
    # Add balances to users
    total_expected = Decimal("0")
    for i, user in enumerate(users):
        user_account = await ledger_service.get_or_create_user_balance_account(user.id)
        amount = Decimal(f"{(i + 1) * 100}.00")
        
        lines = [
            Line(account_id=str(core_accounts["platform_cash"].id), credit=amount),
            Line(account_id=str(user_account.id), debit=amount)
        ]
        
        await ledger_service.post_double_entry(
            lines=lines,
            ref_type="test",
            ref_id=f"float_test_{i}"
        )
        
        total_expected += amount
    
    # Calculate total wallet float
    total_float = await ledger_service.get_total_wallet_float()
    assert total_float == total_expected


@pytest.mark.asyncio
async def test_journal_pagination(db_session, test_user):
    """Test journal entry pagination"""
    ledger_service = LedgerService(db_session)
    
    core_accounts = await ledger_service.get_or_create_core_accounts()
    user_account = await ledger_service.get_or_create_user_balance_account(test_user.id)
    
    # Create multiple journal entries
    for i in range(10):
        amount = Decimal(f"{i + 1}.00")
        lines = [
            Line(account_id=str(core_accounts["platform_cash"].id), credit=amount),
            Line(account_id=str(user_account.id), debit=amount)
        ]
        
        await ledger_service.post_double_entry(
            lines=lines,
            ref_type="test",
            ref_id=f"pagination_test_{i}"
        )
    
    # Test pagination
    entries_page1 = await ledger_service.get_journal_entries(limit=5, offset=0)
    entries_page2 = await ledger_service.get_journal_entries(limit=5, offset=5)
    
    assert len(entries_page1) == 5
    assert len(entries_page2) == 5
    
    # Verify no overlap
    page1_ids = {entry.id for entry in entries_page1}
    page2_ids = {entry.id for entry in entries_page2}
    assert page1_ids.isdisjoint(page2_ids)
