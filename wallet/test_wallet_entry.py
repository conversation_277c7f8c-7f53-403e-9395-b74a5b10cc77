#!/usr/bin/env python3
"""
Simple test script for the wallet entry API
"""
import asyncio
import httpx
import json
from decimal import Decimal


async def test_wallet_entry_api():
    """Test the wallet entry API endpoints"""
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        print("🧪 Testing Wallet Entry API")
        print("=" * 50)
        
        # Test data
        user_id = "test_user_123"
        
        # Test 1: Get initial balance (should be 0 for new user)
        print("\n1. Getting initial balance...")
        response = await client.get(f"{base_url}/wallet-entry/balance/{user_id}")
        if response.status_code == 200:
            balance_data = response.json()
            print(f"✅ Initial balance: {balance_data['current_balance']}")
        else:
            print(f"❌ Failed to get balance: {response.status_code} - {response.text}")
            return
        
        # Test 2: Credit wallet (add money)
        print("\n2. Adding money to wallet...")
        credit_request = {
            "user_id": user_id,
            "related_id": "test_credit_001",
            "order_id": "order_123",
            "wallet_amt": 1000.50,
            "entry_type": "credit",
            "description": "Test credit transaction",
            "reference_type": "test_credit"
        }
        
        response = await client.post(
            f"{base_url}/wallet-entry/add-amount",
            json=credit_request
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Credit successful: {result['amount_processed']}")
            print(f"   New balance: {result['new_balance']}")
            print(f"   Transaction ID: {result['entry_id']}")
        else:
            print(f"❌ Credit failed: {response.status_code} - {response.text}")
            return
        
        # Test 3: Get balance after credit
        print("\n3. Getting balance after credit...")
        response = await client.get(f"{base_url}/wallet-entry/balance/{user_id}")
        if response.status_code == 200:
            balance_data = response.json()
            print(f"✅ Balance after credit: {balance_data['current_balance']}")
        else:
            print(f"❌ Failed to get balance: {response.status_code}")
        
        # Test 4: Debit wallet (subtract money) - valid amount
        print("\n4. Subtracting money from wallet (valid amount)...")
        debit_request = {
            "user_id": user_id,
            "related_id": "test_debit_001",
            "wallet_amt": -250.25,
            "entry_type": "debit",
            "description": "Test debit transaction",
            "reference_type": "test_debit"
        }
        
        response = await client.post(
            f"{base_url}/wallet-entry/add-amount",
            json=debit_request
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Debit successful: {result['amount_processed']}")
            print(f"   New balance: {result['new_balance']}")
        else:
            print(f"❌ Debit failed: {response.status_code} - {response.text}")
        
        # Test 5: Try to debit more than available balance
        print("\n5. Attempting to debit more than available balance...")
        invalid_debit_request = {
            "user_id": user_id,
            "related_id": "test_debit_002",
            "wallet_amt": -2000.00,  # More than available balance
            "entry_type": "debit",
            "description": "Test invalid debit transaction",
            "reference_type": "test_debit"
        }
        
        response = await client.post(
            f"{base_url}/wallet-entry/add-amount",
            json=invalid_debit_request
        )
        
        if response.status_code == 400:
            error = response.json()
            print(f"✅ Insufficient balance error caught: {error['detail']}")
        else:
            print(f"❌ Expected 400 error but got: {response.status_code}")
        
        # Test 6: Final balance check
        print("\n6. Final balance check...")
        response = await client.get(f"{base_url}/wallet-entry/balance/{user_id}")
        if response.status_code == 200:
            balance_data = response.json()
            print(f"✅ Final balance: {balance_data['current_balance']}")
        else:
            print(f"❌ Failed to get final balance: {response.status_code}")
        
        print("\n" + "=" * 50)
        print("🎉 Wallet Entry API test completed!")


if __name__ == "__main__":
    asyncio.run(test_wallet_entry_api())
