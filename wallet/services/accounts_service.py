from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional
import logging

from models.user import User

logger = logging.getLogger(__name__)


class AccountsService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.logger = logging.getLogger(__name__)

    async def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            stmt = select(User).where(User.id == user_id)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if user:
                self.logger.debug(f"Found user: {user_id}")
            else:
                self.logger.debug(f"User not found: {user_id}")
                
            return user
        except Exception as e:
            self.logger.error(f"Error getting user {user_id}: {e}")
            raise

    async def create_user(self, user_id: str, name: Optional[str] = None, kyc_status: str = "pending") -> User:
        """Create a new user"""
        try:
            # Check if user already exists
            existing_user = await self.get_user(user_id)
            if existing_user:
                self.logger.info(f"User {user_id} already exists, returning existing user")
                return existing_user

            # Create new user
            user = User(
                id=user_id,
                kyc_status=kyc_status
            )
            
            self.db.add(user)
            await self.db.flush()  # Flush to get the ID without committing
            
            self.logger.info(f"Created new user: {user_id} with name: {name}")
            return user
            
        except Exception as e:
            self.logger.error(f"Error creating user {user_id}: {e}")
            raise

    async def get_or_create_user(self, user_id: str, name: Optional[str] = None, kyc_status: str = "pending") -> User:
        """Get existing user or create new one if doesn't exist"""
        try:
            user = await self.get_user(user_id)
            if user:
                return user
            
            return await self.create_user(user_id, name, kyc_status)
            
        except Exception as e:
            self.logger.error(f"Error getting or creating user {user_id}: {e}")
            raise

    async def update_user(self, user_id: str, name: Optional[str] = None, kyc_status: Optional[str] = None) -> Optional[User]:
        """Update user information"""
        try:
            user = await self.get_user(user_id)
            if not user:
                self.logger.warning(f"Cannot update user {user_id}: user not found")
                return None

            # Update fields if provided
            if kyc_status is not None:
                user.kyc_status = kyc_status

            await self.db.flush()
            self.logger.info(f"Updated user {user_id}")
            return user
            
        except Exception as e:
            self.logger.error(f"Error updating user {user_id}: {e}")
            raise
