from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal
from typing import Optional
import uuid

from models import User
from services.ledger_service import LedgerService, Line
from schemas.wallet_entry import WalletEntryRequest, WalletEntryResponse, WalletEntryType


class InsufficientBalanceError(Exception):
    """Raised when attempting to debit more than available balance"""
    pass


class WalletEntryService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.ledger_service = LedgerService(db)

    async def process_wallet_entry(self, request: WalletEntryRequest) -> WalletEntryResponse:
        """
        Process wallet entry (credit/debit) with proper validation
        """
        # Get or create user (auto-creation as per existing pattern)
        user = await self._get_or_create_user(request.user_id)
        
        # Ensure user is committed to database before creating ledger account
        await self.db.flush()
        
        # Get user's balance account
        user_account = await self.ledger_service.get_or_create_user_balance_account(request.user_id)
        
        # Get current balance
        current_balance = await self.ledger_service.get_user_balance(request.user_id)
        
        # Validate balance for debit operations
        if request.wallet_amt < 0:  # Debit operation
            debit_amount = abs(request.wallet_amt)
            if current_balance < debit_amount:
                raise InsufficientBalanceError(
                    f"Insufficient balance. Current: {current_balance}, Required: {debit_amount}"
                )
        
        # Get platform cash account for double-entry bookkeeping
        core_accounts = await self.ledger_service.get_or_create_core_accounts()
        platform_cash_account = core_accounts["platform_cash"]
        
        # Prepare journal entry lines
        lines = []
        
        if request.wallet_amt > 0:  # Credit operation (add money to wallet)
            # Credit user balance (increase asset), Debit platform cash (decrease asset)
            lines = [
                Line(account_id=str(user_account.id), debit=request.wallet_amt),
                Line(account_id=str(platform_cash_account.id), credit=request.wallet_amt)
            ]
        else:  # Debit operation (subtract money from wallet)
            debit_amount = abs(request.wallet_amt)
            # Credit user balance (decrease asset), Debit platform cash (increase asset)
            lines = [
                Line(account_id=str(user_account.id), credit=debit_amount),
                Line(account_id=str(platform_cash_account.id), debit=debit_amount)
            ]
        
        # Generate reference ID for the transaction
        transaction_ref = f"{request.reference_type}_{request.related_id}"
        
        # Create journal entries
        journal_entries = await self.ledger_service.post_double_entry(
            lines=lines,
            ref_type=request.reference_type,
            ref_id=request.related_id,
            custom_idem_key=request.idempotency_key
        )
        
        # Get new balance
        new_balance = await self.ledger_service.get_user_balance(request.user_id)
        
        # Commit the transaction
        await self.db.commit()
        
        return WalletEntryResponse(
            success=True,
            message=f"Wallet {'credited' if request.wallet_amt > 0 else 'debited'} successfully",
            user_id=request.user_id,
            amount_processed=request.wallet_amt,
            new_balance=new_balance,
            entry_id=str(journal_entries[0].entry_id),
            transaction_reference=transaction_ref
        )

    async def get_wallet_balance(self, user_id: str) -> Decimal:
        """Get current wallet balance for a user"""
        # Ensure user exists before getting balance
        await self._get_or_create_user(user_id)
        await self.db.flush()
        return await self.ledger_service.get_user_balance(user_id)

    async def _get_or_create_user(self, user_id: str) -> User:
        """Get or create user (following existing pattern from payment service)"""
        from sqlalchemy import select
        
        # Check if user exists
        stmt = select(User).where(User.id == user_id)
        result = await self.db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            # Auto-create user with pending KYC status
            user = User(
                id=user_id,
                name=None,  # Will be updated when user provides details
                kyc_status="pending"
            )
            self.db.add(user)
            await self.db.flush()
            
            # Create user's balance ledger account
            await self.ledger_service.get_or_create_user_balance_account(user_id)
        
        return user
