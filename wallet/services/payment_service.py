from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Dict, Any, Optional
from decimal import Decimal
import uuid
import sentry_sdk
import logging

from models import Payment, User
from services.ledger_service import LedgerService, Line
from services.accounts_service import AccountsService
from providers.razorpay_payments import RazorpayPaymentsProvider
from providers.razorpay_virtual_accounts import RazorpayVirtualAccountsProvider

logger = logging.getLogger(__name__)


class PaymentService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.ledger_service = LedgerService(db)
        self.accounts_service = AccountsService(db)
        self.razorpay_payments = RazorpayPaymentsProvider()
        self.razorpay_va = RazorpayVirtualAccountsProvider()
        self.logger = logging.getLogger(__name__)

    async def _extract_user_id_from_webhook(self, payment_data: Dict[str, Any], webhook_payload: Dict[str, Any]) -> Optional[str]:
        """Extract user_id from webhook payload using customer_id from Razorpay"""
        
        # Strategy 1: Check customer_id in payment notes (primary location)
        payment_notes = webhook_payload.get("payload", {}).get("payment", {}).get("entity", {}).get("notes", {})
        customer_id = payment_notes.get("customer_id")
        if customer_id:
            return customer_id
        
        return None

    async def create_razorpay_order(
        self, 
        user_id: str, 
        amount: Decimal, 
        receipt: Optional[str] = None, 
        notes: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Create a Razorpay order for user topup"""
        
        # Set Sentry context for better error tracking
        with sentry_sdk.configure_scope() as scope:
            scope.set_user({"id": user_id})
            scope.set_tag("operation", "create_razorpay_order")
            scope.set_context("payment", {
                "amount": str(amount),
                "receipt": receipt,
                "user_id": user_id
            })
        
        # Get or create user
        stmt = select(User).where(User.id == user_id)
        result = await self.db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            # Auto-create user if they don't exist
            user = User(id=user_id, kyc_status="pending")
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            
            # Create user's balance account
            await self.ledger_service.get_or_create_user_balance_account(user_id)

        # Convert amount to paise (Razorpay uses paise)
        amount_paise = int(amount * 100)
        
        # Create order with Razorpay
        order_data = await self.razorpay_payments.create_order(
            amount_paise=amount_paise,
            receipt=receipt,
            notes=notes
        )
        
        # Store payment record
        payment = Payment(
            gateway="razorpay",
            order_id=order_data["id"],
            status="created",
            amount=amount,
            user_id=user_id
        )
        self.db.add(payment)
        await self.db.commit()
        
        return {
            "payment_id": str(payment.id),
            "order_id": order_data["id"],
            "amount": amount,
            "currency": "INR",
            "receipt": receipt,
            "razorpay_order": order_data
        }

    async def process_razorpay_payment_captured(self, webhook_payload: Dict[str, Any]) -> bool:
        """Process Razorpay payment.captured webhook"""
        
        try:
            # Parse payment data
            payment_data = self.razorpay_payments.parse_payment_captured_event(webhook_payload)
            
            # Set Sentry context for webhook processing
            with sentry_sdk.configure_scope() as scope:
                scope.set_tag("operation", "process_payment_captured")
                scope.set_context("webhook", {
                    "order_id": payment_data.get("order_id"),
                    "payment_id": payment_data.get("payment_id"),
                    "amount": str(payment_data.get("amount")),
                    "captured": payment_data.get("captured")
                })
                
            self.logger.info(f"Processing payment capture for order: {payment_data.get('order_id')}")
            
        except Exception as e:
            self.logger.error(f"Error parsing webhook payload: {str(e)}")
            sentry_sdk.capture_exception(e)
            raise
        
        if not payment_data.get("captured"):
            return False  # Payment not captured
        
        # Find existing payment record
        stmt = select(Payment).where(Payment.order_id == payment_data["order_id"])
        result = await self.db.execute(stmt)
        payment = result.scalar_one_or_none()
        
        if not payment:
            # Payment record doesn't exist - create it from webhook data
            # Try to get customer_id and customer_name from webhook
            customer_id = payment_data.get("customer_id")
            customer_name = payment_data.get("customer_name")
            
            # Fallback to existing user_id extraction if customer_id not available
            user_id = customer_id or await self._extract_user_id_from_webhook(payment_data, webhook_payload)
            
            if not user_id:
                logger.warning(f"Cannot create payment record for order {payment_data['order_id']}: customer_id/user_id not found in webhook payload")
                return False
            
            # Create or get user from webhook data
            user = await self.accounts_service.get_or_create_user(user_id, customer_name)
            
            # Create new payment record from webhook data
            payment = Payment(
                gateway="razorpay",
                order_id=payment_data["order_id"],
                payment_id=payment_data["payment_id"],
                status="captured",
                amount=payment_data["amount"],
                fee=payment_data["fee"],
                tax=payment_data["tax"],
                user_id=user_id,
                raw_webhook=webhook_payload
            )
            self.db.add(payment)
            
            logger.info(f"Created payment record from webhook for order {payment_data['order_id']}, user {user_id}, amount {payment_data['amount']}")
        
        elif payment.status == "captured":
            return True  # Already processed (idempotency)
        
        else:
            # Update existing payment record
            payment.payment_id = payment_data["payment_id"]
            payment.status = "captured"
            payment.fee = payment_data["fee"]
            payment.tax = payment_data["tax"]
            payment.raw_webhook = webhook_payload
            
            logger.info(f"Updated existing payment record for order {payment_data['order_id']}, user {payment.user_id}")
        
        # Get core accounts
        core_accounts = await self.ledger_service.get_or_create_core_accounts()
        user_account = await self.ledger_service.get_or_create_user_balance_account(payment.user_id)
        
        # Post double-entry journal entries
        # 1. escrow_receivable -> platform_cash (settlement)
        # 2. platform_cash -> user_balance (credit user)
        
        net_amount = payment.amount - payment.fee
        
        lines = [
            # Settlement: Move from escrow to platform cash
            Line(account_id=str(core_accounts["escrow_receivable"].id), credit=payment.amount),
            Line(account_id=str(core_accounts["platform_cash"].id), debit=payment.amount),
            
            # Fee collection: Move fee from platform cash to fees account
            Line(account_id=str(core_accounts["platform_cash"].id), credit=payment.fee),
            Line(account_id=str(core_accounts["fees"].id), debit=payment.fee),
            
            # User credit: Move net amount from platform cash to user balance
            Line(account_id=str(core_accounts["platform_cash"].id), credit=net_amount),
            Line(account_id=str(user_account.id), debit=net_amount)  # DEBIT to increase user balance
        ]
        
        await self.ledger_service.post_double_entry(
            lines=lines,
            ref_type="payment",
            ref_id=payment_data["payment_id"]
        )
        
        await self.db.commit()
        return True

    async def handle_virtual_account_credit(self, webhook_payload: Dict[str, Any]) -> bool:
        """Handle Razorpay virtual account credit webhook"""
        
        # Parse VA credit data
        va_data = self.razorpay_va.parse_virtual_account_credited_event(webhook_payload)
        
        user_id = va_data.get("user_id")
        if not user_id:
            raise ValueError("User ID not found in virtual account webhook")
        
        # Check if already processed
        payment_id = va_data["payment_id"]
        stmt = select(Payment).where(Payment.payment_id == payment_id)
        result = await self.db.execute(stmt)
        existing_payment = result.scalar_one_or_none()
        
        if existing_payment:
            logger.info(f"Virtual account credit already processed for payment {payment_id}, user {user_id}")
            return True  # Already processed
        
        # Verify user exists
        user_stmt = select(User).where(User.id == user_id)
        user_result = await self.db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        if not user:
            logger.warning(f"Cannot create VA credit record for payment {payment_id}: user {user_id} not found")
            return False
        
        # Create payment record for VA credit
        payment = Payment(
            gateway="razorpay_va",
            order_id=va_data["virtual_account_id"],
            payment_id=payment_id,
            status="captured",
            amount=va_data["amount"],
            fee=va_data["fee"],
            tax=va_data["tax"],
            user_id=user_id,
            raw_webhook=webhook_payload
        )
        self.db.add(payment)
        
        logger.info(f"Created VA credit payment record for payment {payment_id}, user {user_id}, amount {va_data['amount']}")
        
        # Get accounts
        core_accounts = await self.ledger_service.get_or_create_core_accounts()
        user_account = await self.ledger_service.get_or_create_user_balance_account(user_id)
        
        # Post journal entries for VA credit
        net_amount = payment.amount - payment.fee
        
        lines = [
            # Settlement from escrow
            Line(account_id=str(core_accounts["escrow_receivable"].id), credit=payment.amount),
            Line(account_id=str(core_accounts["platform_cash"].id), debit=payment.amount),
            
            # Fee collection
            Line(account_id=str(core_accounts["platform_cash"].id), credit=payment.fee),
            Line(account_id=str(core_accounts["fees"].id), debit=payment.fee),
            
            # User credit
            Line(account_id=str(core_accounts["platform_cash"].id), credit=net_amount),
            Line(account_id=str(user_account.id), debit=net_amount)
        ]
        
        await self.ledger_service.post_double_entry(
            lines=lines,
            ref_type="va_credit",
            ref_id=payment_id
        )
        
        await self.db.commit()
        return True

    async def create_virtual_account(self, user_id: str) -> Dict[str, Any]:
        """Create virtual account for a user"""
        
        # Get or create user
        stmt = select(User).where(User.id == user_id)
        result = await self.db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            # Auto-create user if they don't exist
            user = User(id=user_id, kyc_status="pending")
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            
            # Create user's balance account
            await self.ledger_service.get_or_create_user_balance_account(user_id)
        
        # Create VA with Razorpay
        va_data = await self.razorpay_va.create_virtual_account(user_id)
        
        # Extract bank account details
        bank_account = va_data.get("receivers", [{}])[0]
        
        return {
            "virtual_account_id": va_data["id"],
            "account_number": bank_account.get("account"),
            "ifsc": bank_account.get("ifsc"),
            "bank_name": bank_account.get("bank_name"),
            "user_id": user_id,
            "status": va_data["status"]
        }
