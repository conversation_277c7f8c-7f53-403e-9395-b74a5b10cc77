from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from decimal import Decimal
import uuid
import hashlib

from models import LedgerAccount, JournalEntry, BalanceSnapshot, User
from settings import settings


class Line:
    """Represents a single line in a double-entry journal entry"""
    def __init__(self, account_id: str, debit: Decimal = Decimal('0'), credit: Decimal = Decimal('0')):
        self.account_id = account_id
        self.debit = debit
        self.credit = credit
        
        if debit > 0 and credit > 0:
            raise ValueError("A line cannot have both debit and credit amounts")
        if debit == 0 and credit == 0:
            raise ValueError("A line must have either debit or credit amount")


class LedgerService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_or_create_core_accounts(self) -> Dict[str, LedgerAccount]:
        """Get or create platform core accounts"""
        core_account_types = [
            "platform_cash",
            "fees", 
            "escrow_receivable"
        ]
        
        accounts = {}
        for account_type in core_account_types:
            stmt = select(LedgerAccount).where(
                LedgerAccount.user_id.is_(None),
                LedgerAccount.type == account_type,
                LedgerAccount.currency == "INR"
            )
            result = await self.db.execute(stmt)
            account = result.scalar_one_or_none()
            
            if not account:
                account = LedgerAccount(
                    user_id=None,
                    type=account_type,
                    currency="INR"
                )
                self.db.add(account)
                await self.db.flush()
            
            accounts[account_type] = account
        
        return accounts

    async def get_or_create_user_balance_account(self, user_id: str) -> LedgerAccount:
        """Get or create user balance account"""
        stmt = select(LedgerAccount).where(
            LedgerAccount.user_id == user_id,
            LedgerAccount.type == "user_balance",
            LedgerAccount.currency == "INR"
        )
        result = await self.db.execute(stmt)
        account = result.scalar_one_or_none()
        
        if not account:
            account = LedgerAccount(
                user_id=user_id,
                type="user_balance",
                currency="INR"
            )
            self.db.add(account)
            await self.db.flush()
        
        return account

    def _generate_idempotency_key(self, ref_type: str, ref_id: str, line_index: int = 0) -> str:
        """Generate idempotency key for journal entry"""
        raw_key = f"{ref_type}_{ref_id}_{line_index}_{settings.service_idempotency_salt}"
        return hashlib.sha256(raw_key.encode()).hexdigest()

    async def post_double_entry(
        self, 
        lines: List[Line], 
        ref_type: str, 
        ref_id: str, 
        custom_idem_key: Optional[str] = None
    ) -> List[JournalEntry]:
        """Post a balanced double-entry transaction"""
        
        # Validate that debits equal credits
        total_debits = sum(line.debit for line in lines)
        total_credits = sum(line.credit for line in lines)
        
        if total_debits != total_credits:
            raise ValueError(f"Unbalanced entry: debits={total_debits}, credits={total_credits}")
        
        # Generate entry ID for grouping related entries
        entry_id = uuid.uuid4()
        journal_entries = []
        
        for i, line in enumerate(lines):
            # Generate idempotency key
            if custom_idem_key:
                idem_key = f"{custom_idem_key}_{i}"
            else:
                idem_key = self._generate_idempotency_key(ref_type, ref_id, i)
            
            # Check if entry already exists (idempotency)
            stmt = select(JournalEntry).where(JournalEntry.idempotency_key == idem_key)
            result = await self.db.execute(stmt)
            existing_entry = result.scalar_one_or_none()
            
            if existing_entry:
                journal_entries.append(existing_entry)
                continue
            
            # Create new journal entry
            journal_entry = JournalEntry(
                entry_id=entry_id,
                account_id=line.account_id,
                debit=line.debit,
                credit=line.credit,
                currency="INR",
                ref_type=ref_type,
                ref_id=ref_id,
                idempotency_key=idem_key
            )
            
            self.db.add(journal_entry)
            journal_entries.append(journal_entry)
        
        await self.db.flush()
        return journal_entries

    async def get_account_balance(self, account_id: str) -> Decimal:
        """Calculate account balance from journal entries"""
        # Get account type to determine balance calculation
        account_stmt = select(LedgerAccount).where(LedgerAccount.id == account_id)
        account_result = await self.db.execute(account_stmt)
        account = account_result.scalar_one_or_none()
        
        if not account:
            return Decimal('0')
        
        # For user_balance accounts (assets), balance = debit - credit
        # For liability/equity accounts, balance = credit - debit
        if account.type == "user_balance":
            stmt = select(
                func.sum(JournalEntry.debit - JournalEntry.credit)
            ).where(JournalEntry.account_id == account_id)
        else:
            stmt = select(
                func.sum(JournalEntry.credit - JournalEntry.debit)
            ).where(JournalEntry.account_id == account_id)
        
        result = await self.db.execute(stmt)
        balance = result.scalar() or Decimal('0')
        return balance

    async def get_user_balance(self, user_id: str) -> Decimal:
        """Get user's wallet balance"""
        account = await self.get_or_create_user_balance_account(user_id)
        return await self.get_account_balance(str(account.id))

    async def get_all_user_balances(self) -> List[Dict[str, Any]]:
        """Get balances for all users"""
        stmt = select(
            LedgerAccount.user_id,
            func.sum(JournalEntry.debit - JournalEntry.credit).label('balance')  # Fixed: debit - credit for assets
        ).join(
            JournalEntry, LedgerAccount.id == JournalEntry.account_id
        ).where(
            LedgerAccount.type == "user_balance",
            LedgerAccount.user_id.is_not(None)
        ).group_by(LedgerAccount.user_id)
        
        result = await self.db.execute(stmt)
        return [{"user_id": row.user_id, "balance": row.balance or Decimal('0')} 
                for row in result.fetchall()]

    async def get_total_wallet_float(self) -> Decimal:
        """Get total wallet float (sum of all user balances)"""
        stmt = select(
            func.sum(JournalEntry.debit - JournalEntry.credit)  # Fixed: debit - credit for assets
        ).join(
            LedgerAccount, LedgerAccount.id == JournalEntry.account_id
        ).where(
            LedgerAccount.type == "user_balance",
            LedgerAccount.user_id.is_not(None)
        )
        
        result = await self.db.execute(stmt)
        total = result.scalar() or Decimal('0')
        return total

    async def get_journal_entries(
        self, 
        limit: int = 20, 
        offset: int = 0,
        user_id: Optional[str] = None
    ) -> List[JournalEntry]:
        """Get paginated journal entries"""
        stmt = select(JournalEntry).options(
            selectinload(JournalEntry.account)
        ).order_by(JournalEntry.created_at.desc())
        
        if user_id:
            stmt = stmt.join(LedgerAccount).where(LedgerAccount.user_id == user_id)
        
        stmt = stmt.limit(limit).offset(offset)
        result = await self.db.execute(stmt)
        return result.scalars().all()
