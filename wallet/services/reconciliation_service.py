from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import Dict, Any, List
from decimal import Decimal
from datetime import datetime, timedelta
import logging

from models import Payment, Payout, JournalEntry, LedgerAccount
from services.ledger_service import LedgerService

logger = logging.getLogger(__name__)


class ReconciliationService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.ledger_service = LedgerService(db)

    async def daily_reconciliation(self, date: datetime = None) -> Dict[str, Any]:
        """Perform daily reconciliation between gateway reports and journal"""
        if not date:
            date = datetime.now().date()
        
        start_date = datetime.combine(date, datetime.min.time())
        end_date = start_date + timedelta(days=1)
        
        # Get payment totals from our records
        payment_totals = await self._get_payment_totals(start_date, end_date)
        payout_totals = await self._get_payout_totals(start_date, end_date)
        
        # Get journal totals
        journal_totals = await self._get_journal_totals(start_date, end_date)
        
        # Mock gateway reports (in real implementation, fetch from providers)
        gateway_reports = await self._fetch_gateway_reports(date)
        
        # Compare and find discrepancies
        discrepancies = self._find_discrepancies(
            payment_totals, payout_totals, journal_totals, gateway_reports
        )
        
        # Log results
        reconciliation_result = {
            "date": date.isoformat(),
            "payment_totals": payment_totals,
            "payout_totals": payout_totals,
            "journal_totals": journal_totals,
            "gateway_reports": gateway_reports,
            "discrepancies": discrepancies,
            "status": "success" if not discrepancies else "discrepancies_found"
        }
        
        if discrepancies:
            logger.warning(f"Reconciliation discrepancies found for {date}: {discrepancies}")
        else:
            logger.info(f"Reconciliation successful for {date}")
        
        return reconciliation_result

    async def _get_payment_totals(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get payment totals from our records"""
        stmt = select(
            func.count(Payment.id).label('count'),
            func.sum(Payment.amount).label('total_amount'),
            func.sum(Payment.fee).label('total_fees')
        ).where(
            Payment.status == 'captured',
            Payment.created_at >= start_date,
            Payment.created_at < end_date
        )
        
        result = await self.db.execute(stmt)
        row = result.fetchone()
        
        return {
            "count": row.count or 0,
            "total_amount": float(row.total_amount or 0),
            "total_fees": float(row.total_fees or 0)
        }

    async def _get_payout_totals(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get payout totals from our records"""
        stmt = select(
            func.count(Payout.id).label('count'),
            func.sum(Payout.amount).label('total_amount')
        ).where(
            Payout.status == 'success',
            Payout.created_at >= start_date,
            Payout.created_at < end_date
        )
        
        result = await self.db.execute(stmt)
        row = result.fetchone()
        
        return {
            "count": row.count or 0,
            "total_amount": float(row.total_amount or 0)
        }

    async def _get_journal_totals(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get journal entry totals"""
        # Get totals by reference type
        stmt = select(
            JournalEntry.ref_type,
            func.sum(JournalEntry.debit).label('total_debits'),
            func.sum(JournalEntry.credit).label('total_credits')
        ).where(
            JournalEntry.created_at >= start_date,
            JournalEntry.created_at < end_date
        ).group_by(JournalEntry.ref_type)
        
        result = await self.db.execute(stmt)
        journal_by_type = {}
        
        for row in result.fetchall():
            journal_by_type[row.ref_type] = {
                "total_debits": float(row.total_debits or 0),
                "total_credits": float(row.total_credits or 0)
            }
        
        return journal_by_type

    async def _fetch_gateway_reports(self, date: datetime) -> Dict[str, Any]:
        """Mock function to fetch gateway reports"""
        # In real implementation, this would fetch actual reports from:
        # - Razorpay settlement reports
        # - Cashfree payout reports
        
        return {
            "razorpay": {
                "settlements": {
                    "count": 0,
                    "total_amount": 0,
                    "total_fees": 0
                }
            },
            "cashfree": {
                "payouts": {
                    "count": 0,
                    "total_amount": 0
                }
            }
        }

    def _find_discrepancies(
        self, 
        payment_totals: Dict[str, Any],
        payout_totals: Dict[str, Any],
        journal_totals: Dict[str, Any],
        gateway_reports: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Find discrepancies between our records and gateway reports"""
        discrepancies = []
        
        # Compare payment totals with Razorpay settlements
        razorpay_settlements = gateway_reports.get("razorpay", {}).get("settlements", {})
        if payment_totals["total_amount"] != razorpay_settlements.get("total_amount", 0):
            discrepancies.append({
                "type": "payment_amount_mismatch",
                "our_total": payment_totals["total_amount"],
                "gateway_total": razorpay_settlements.get("total_amount", 0),
                "difference": payment_totals["total_amount"] - razorpay_settlements.get("total_amount", 0)
            })
        
        # Compare payout totals with Cashfree reports
        cashfree_payouts = gateway_reports.get("cashfree", {}).get("payouts", {})
        if payout_totals["total_amount"] != cashfree_payouts.get("total_amount", 0):
            discrepancies.append({
                "type": "payout_amount_mismatch",
                "our_total": payout_totals["total_amount"],
                "gateway_total": cashfree_payouts.get("total_amount", 0),
                "difference": payout_totals["total_amount"] - cashfree_payouts.get("total_amount", 0)
            })
        
        return discrepancies

    async def get_wallet_float_summary(self) -> Dict[str, Any]:
        """Get summary of wallet float and platform balances"""
        # Get total user balances
        total_user_balance = await self.ledger_service.get_total_wallet_float()
        
        # Get platform account balances
        core_accounts = await self.ledger_service.get_or_create_core_accounts()
        platform_balances = {}
        
        for account_type, account in core_accounts.items():
            balance = await self.ledger_service.get_account_balance(str(account.id))
            platform_balances[account_type] = float(balance)
        
        return {
            "total_user_balance": float(total_user_balance),
            "platform_balances": platform_balances,
            "net_platform_position": sum(platform_balances.values())
        }
