from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Dict, Any, Optional
from decimal import Decimal
import uuid

from models import Payout, User
from services.ledger_service import LedgerService, Line
from providers.cashfree_cashgram import CashfreeCashgramProvider


class PayoutService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.ledger_service = LedgerService(db)
        self.cashfree = CashfreeCashgramProvider()

    async def create_cashgram(
        self, 
        user_id: str, 
        amount: Decimal, 
        phone: Optional[str] = None,
        email: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a Cashfree Cashgram for withdrawal"""
        
        # Verify user exists
        stmt = select(User).where(User.id == user_id)
        result = await self.db.execute(stmt)
        user = result.scalar_one_or_none()
        if not user:
            raise ValueError(f"User {user_id} not found")

        # Check user balance
        user_balance = await self.ledger_service.get_user_balance(user_id)
        if user_balance < amount:
            raise ValueError(f"Insufficient balance. Available: {user_balance}, Requested: {amount}")

        # Generate unique cashgram ID
        cashgram_id = f"cg_{uuid.uuid4().hex[:12]}"
        
        # Create cashgram with Cashfree
        cashgram_data = await self.cashfree.create_cashgram(
            cashgram_id=cashgram_id,
            amount_rupees=amount,
            phone=phone,
            email=email
        )
        
        # Store payout record
        payout = Payout(
            provider="cashfree",
            link_id=cashgram_id,
            status="created",
            amount=amount,
            user_id=user_id
        )
        self.db.add(payout)
        await self.db.commit()
        
        return {
            "payout_id": str(payout.id),
            "link_id": cashgram_id,
            "amount": amount,
            "status": "created",
            "cashgram_link": cashgram_data.get("link", ""),
            "expiry_date": cashgram_data.get("expiryDate")
        }

    async def process_cashfree_webhook(self, webhook_payload: Dict[str, Any]) -> bool:
        """Process Cashfree Cashgram webhook"""
        
        # Parse cashgram data
        cashgram_data = self.cashfree.parse_cashgram_webhook_event(webhook_payload)
        
        cashgram_id = cashgram_data["cashgram_id"]
        status = cashgram_data["status"]
        
        # Find existing payout record
        stmt = select(Payout).where(Payout.link_id == cashgram_id)
        result = await self.db.execute(stmt)
        payout = result.scalar_one_or_none()
        
        if not payout:
            raise ValueError(f"Payout not found for cashgram {cashgram_id}")
        
        # Check if already processed
        if payout.status in ["success", "failed", "expired"]:
            return True  # Already processed
        
        # Update payout status
        payout.status = status.lower()
        payout.raw_webhook = webhook_payload
        
        # Process based on status
        if status in ["SUCCESS", "CLAIM_SUCCESS"]:
            await self._process_successful_withdrawal(payout, cashgram_data)
        elif status in ["FAILED", "EXPIRED"]:
            # For failed/expired, we might want to reverse any holds
            # For now, just update status
            pass
        
        await self.db.commit()
        return True

    async def _process_successful_withdrawal(self, payout: Payout, cashgram_data: Dict[str, Any]) -> None:
        """Process successful withdrawal - debit user balance"""
        
        # Get accounts
        core_accounts = await self.ledger_service.get_or_create_core_accounts()
        user_account = await self.ledger_service.get_or_create_user_balance_account(payout.user_id)
        
        # Post journal entries: user_balance -> platform_cash
        lines = [
            Line(account_id=str(user_account.id), credit=payout.amount),
            Line(account_id=str(core_accounts["platform_cash"].id), debit=payout.amount)
        ]
        
        await self.ledger_service.post_double_entry(
            lines=lines,
            ref_type="payout",
            ref_id=payout.link_id
        )

    async def get_user_payouts(
        self, 
        user_id: str, 
        limit: int = 20, 
        offset: int = 0
    ) -> list[Payout]:
        """Get paginated payouts for a user"""
        stmt = select(Payout).where(
            Payout.user_id == user_id
        ).order_by(
            Payout.created_at.desc()
        ).limit(limit).offset(offset)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_all_payouts(
        self, 
        limit: int = 20, 
        offset: int = 0,
        status_filter: Optional[str] = None
    ) -> list[Payout]:
        """Get paginated payouts for admin"""
        stmt = select(Payout).order_by(Payout.created_at.desc())
        
        if status_filter:
            stmt = stmt.where(Payout.status == status_filter)
        
        stmt = stmt.limit(limit).offset(offset)
        result = await self.db.execute(stmt)
        return result.scalars().all()
