# Wallet Service User Guide

## Overview

The Wallet Service is a production-ready microservice that provides a complete digital wallet solution with payment gateway integrations, double-entry ledger accounting, and a web-based admin interface.

## Features

- 💰 **Add Money**: Via Razorpay (Checkout + Virtual Accounts/Autocollect)
- 💸 **Withdraw Money**: Via Cashfree Cashgram
- 📊 **Double-Entry Ledger**: Append-only accounting with idempotency
- 🌐 **Admin Web UI**: Complete dashboard for wallet management
- 🔐 **Secure Authentication**: Username/password login for admin interface
- 🔗 **Webhook Support**: Real-time payment and payout processing
- 🐳 **Docker Ready**: Easy deployment with PostgreSQL

---

## Quick Start

### 1. Local Development Setup

```bash
# Clone and setup
cd wallet-service
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Setup database
docker-compose up -d db
python3 -m alembic upgrade head
python3 seed_data.py

# Start server
python3 -m uvicorn app:app --host 0.0.0.0 --port 8000 --reload
```

### 2. Docker Deployment

```bash
# Start everything with Docker
docker-compose up -d

# The application will be available at http://localhost:8000
```

---

## Admin Web Interface

### Accessing the Admin Dashboard

1. **URL**: http://localhost:8000/admin
2. **Login Credentials**:
   - Username: `admin`
   - Password: `admin123`

### Dashboard Features

#### 🏠 **Dashboard**
- **Total Wallet Float**: Sum of all user balances
- **User Statistics**: Total users and KYC status breakdown
- **Transaction Metrics**: Recent topups and withdrawals (30 days)
- **Quick Actions**: Direct links to key management sections

#### 👥 **Users Management**
- View all registered users
- Check KYC status (verified, pending, rejected)
- See user creation timestamps
- Monitor user account status

#### 💰 **Balance Overview**
- **User Balances**: Individual wallet balances for all users
- **Platform Accounts**: 
  - Platform Cash: Money held by the platform
  - Fees: Transaction fees collected
  - Escrow Receivable: Pending settlements

#### 📋 **Journal Entries**
- Complete double-entry ledger view
- All debits and credits with timestamps
- Reference tracking (payment IDs, payout IDs)
- Idempotency key verification
- Account-wise transaction filtering

#### 💳 **Payments**
- Razorpay payment transactions
- Payment status tracking (created, captured, failed)
- Amount and currency details
- External payment ID references

#### 💸 **Payouts**
- Cashfree withdrawal transactions
- Payout status monitoring
- Beneficiary details
- Processing timestamps

---

## API Endpoints

### Authentication

All API endpoints require authentication via header:
```
X-API-Key: supersecretadminkey
```

### Core Endpoints

#### 1. Create Topup Order
```http
POST /api/topups/order
Content-Type: application/json

{
    "user_id": "alice_123",
    "amount": 1000.00,
    "currency": "INR"
}
```

**Response:**
```json
{
    "order_id": "order_xyz123",
    "amount": 1000.00,
    "currency": "INR",
    "razorpay_order_id": "order_razorpay_abc",
    "status": "created"
}
```

#### 2. Create Withdrawal
```http
POST /api/withdrawals
Content-Type: application/json

{
    "user_id": "alice_123",
    "amount": 500.00,
    "currency": "INR",
    "beneficiary": {
        "name": "Alice Smith",
        "email": "<EMAIL>",
        "phone": "**********"
    }
}
```

**Response:**
```json
{
    "payout_id": "payout_xyz789",
    "amount": 500.00,
    "currency": "INR",
    "status": "processing",
    "cashfree_transfer_id": "cf_transfer_123"
}
```

### Webhook Endpoints

#### Razorpay Webhook
```http
POST /webhooks/razorpay
Content-Type: application/json
X-Razorpay-Signature: <signature>

{
    "event": "payment.captured",
    "payload": {
        "payment": {
            "entity": {
                "id": "pay_xyz123",
                "amount": 100000,
                "currency": "INR",
                "status": "captured"
            }
        }
    }
}
```

#### Cashfree Webhook
```http
POST /webhooks/cashfree
Content-Type: application/json
X-CF-Signature: <signature>

{
    "type": "TRANSFER_SUCCESS",
    "data": {
        "transfer": {
            "transferId": "cf_transfer_123",
            "status": "SUCCESS",
            "amount": 500.00
        }
    }
}
```

---

## Payment Flow Examples

### Adding Money (Topup)

1. **Create Order**: Call `/api/topups/order` to create a Razorpay order
2. **Payment Processing**: User completes payment via Razorpay Checkout
3. **Webhook Processing**: Razorpay sends webhook on payment capture
4. **Balance Update**: User's wallet balance is credited automatically

### Withdrawing Money (Payout)

1. **Create Withdrawal**: Call `/api/withdrawals` with beneficiary details
2. **Balance Check**: System verifies sufficient balance
3. **Cashfree Processing**: Payout created via Cashfree Cashgram
4. **Webhook Confirmation**: Cashfree sends status updates via webhook
5. **Balance Update**: User's wallet balance is debited on success

---

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```bash
# Database
DB_URL=postgresql+asyncpg://wallet_user:wallet_pass@localhost:5432/wallet

# Razorpay (Payments + Virtual Accounts)
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret
RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret
RAZORPAY_BASE=https://api.razorpay.com/v1

# Cashfree (Cashgram)
CASHFREE_CLIENT_ID=your_cashfree_client_id
CASHFREE_CLIENT_SECRET=your_cashfree_secret
CASHFREE_WEBHOOK_SECRET=your_cashfree_webhook_secret
CASHFREE_BASE=https://payout-api.cashfree.com

# Admin Authentication
ADMIN_API_KEY=supersecretadminkey

# Security
SERVICE_IDEMPOTENCY_SALT=your_random_salt_string

# Development
DEBUG=false
LOG_LEVEL=INFO
```

### Production Considerations

1. **Change Default Credentials**: Update admin username/password in production
2. **Use HTTPS**: Enable SSL/TLS for secure communication
3. **Environment Variables**: Use secure secret management for API keys
4. **Database**: Use managed PostgreSQL service for production
5. **Monitoring**: Add logging and monitoring for transaction tracking

---

## Troubleshooting

### Common Issues

#### 1. Database Connection Error
```
Solution: Ensure PostgreSQL is running and connection string is correct
```

#### 2. Authentication Failed
```
Solution: Verify X-API-Key header or admin login credentials
```

#### 3. Webhook Signature Verification Failed
```
Solution: Check webhook secret configuration in .env file
```

#### 4. Insufficient Balance for Withdrawal
```
Solution: Ensure user has sufficient balance before creating payout
```

### Logs and Debugging

- **Application Logs**: Check console output for detailed error messages
- **Database Logs**: Monitor PostgreSQL logs for query issues
- **Webhook Logs**: Verify webhook payload and signature verification

---

## Testing

### Using cURL

#### Test Topup Order Creation
```bash
curl -X POST http://localhost:8000/api/topups/order \
  -H "Content-Type: application/json" \
  -H "X-API-Key: supersecretadminkey" \
  -d '{
    "user_id": "alice_123",
    "amount": 1000.00,
    "currency": "INR"
  }'
```

#### Test Withdrawal
```bash
curl -X POST http://localhost:8000/api/withdrawals \
  -H "Content-Type: application/json" \
  -H "X-API-Key: supersecretadminkey" \
  -d '{
    "user_id": "alice_123",
    "amount": 500.00,
    "currency": "INR",
    "beneficiary": {
      "name": "Alice Smith",
      "email": "<EMAIL>",
      "phone": "**********"
    }
  }'
```

### Demo Data

The application includes demo data with:
- 5 sample users (alice_123, bob_456, charlie_789, diana_101, eve_202)
- Sample transactions and balances
- Platform accounts with initial balances

---

## Support

For issues or questions:
1. Check the logs for detailed error messages
2. Verify configuration in `.env` file
3. Ensure all dependencies are installed correctly
4. Review the API documentation for correct request formats

---

## Security Notes

- **API Keys**: Keep all API keys and secrets secure
- **Webhooks**: Always verify webhook signatures
- **Database**: Use strong passwords and secure connections
- **Admin Access**: Change default admin credentials in production
- **HTTPS**: Use SSL/TLS in production environments

---

*Built with FastAPI, PostgreSQL, and modern web technologies for a robust wallet service solution.*
