# Authentication Middleware Guide

This guide explains how to use the authentication middleware that supports both internal API key and Firebase auth token authentication.

## Overview

The authentication middleware provides a flexible authentication system with two methods:

1. **Internal API Key**: For service-to-service communication
2. **Firebase Auth Token**: For user authentication via Firebase

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
# Internal API key for service-to-service auth
INTERNAL_API_KEY=your-secure-internal-api-key

# Firebase credentials (optional)
FIREBASE_CREDENTIALS_PATH=/path/to/firebase-service-account.json
```

### Firebase Setup (Optional)

1. Create a Firebase project
2. Generate a service account key JSON file
3. Set the `FIREBASE_CREDENTIALS_PATH` environment variable

## Authentication Methods

### 1. Internal API Key Authentication

Use the `X-API-Key` header:

```bash
curl -X GET "http://localhost:8000/protected-endpoint" \
  -H "X-API-Key: your-secure-internal-api-key"
```

### 2. Firebase Auth Token Authentication

Use the `Authorization` header with Bearer token:

```bash
curl -X GET "http://localhost:8000/protected-endpoint" \
  -H "Authorization: Bearer firebase-id-token"
```

## Usage in FastAPI Routes

### Required Authentication

```python
from fastapi import APIRouter, Depends
from deps import AuthenticatedUser

router = APIRouter()

@router.get("/protected")
async def protected_endpoint(current_user: AuthenticatedUser):
    return {
        "message": f"Hello {current_user['user_id']}",
        "auth_type": current_user["auth_type"],
        "role": current_user["role"]
    }
```

### Optional Authentication

```python
from deps import OptionalAuthenticatedUser

@router.get("/optional-auth")
async def optional_endpoint(current_user: OptionalAuthenticatedUser):
    if current_user:
        return {"message": f"Hello {current_user['user_id']}"}
    else:
        return {"message": "Hello anonymous user"}
```

### Permission-Based Access

```python
from middleware.auth_middleware import require_permission

@router.post("/admin-only")
@require_permission("admin")
async def admin_endpoint(current_user: AuthenticatedUser):
    return {"message": "Admin access granted"}
```

### Role-Based Access

```python
from middleware.auth_middleware import require_role

@router.post("/service-only")
@require_role("service")
async def service_endpoint(current_user: AuthenticatedUser):
    return {"message": "Service access granted"}
```

## User Object Structure

### Internal API Key Authentication

```python
{
    "auth_type": "internal_api_key",
    "user_id": "internal_service",
    "role": "service",
    "permissions": ["read", "write", "admin"]
}
```

### Firebase Authentication

```python
{
    "auth_type": "firebase",
    "user_id": "firebase-uid",
    "email": "<EMAIL>",
    "email_verified": True,
    "role": "user",
    "permissions": ["read", "write"],
    "firebase_claims": {
        # Full Firebase token claims
    }
}
```

## Error Responses

### 401 Unauthorized

```json
{
    "detail": "No valid authentication provided. Use X-API-Key header or Authorization Bearer token"
}
```

### 403 Forbidden

```json
{
    "detail": "Permission 'admin' required"
}
```

## Example Router Implementation

```python
from fastapi import APIRouter, HTTPException
from deps import AuthenticatedUser, OptionalAuthenticatedUser, DatabaseSession
from middleware.auth_middleware import require_permission

router = APIRouter(prefix="/api/v1", tags=["Protected API"])

@router.get("/profile")
async def get_profile(current_user: AuthenticatedUser):
    """Get current user profile"""
    return {
        "user_id": current_user["user_id"],
        "auth_type": current_user["auth_type"],
        "role": current_user["role"]
    }

@router.get("/public")
async def public_endpoint(current_user: OptionalAuthenticatedUser):
    """Public endpoint with optional auth"""
    if current_user:
        return {"message": f"Hello {current_user['user_id']}"}
    return {"message": "Hello anonymous user"}

@router.post("/admin/users")
@require_permission("admin")
async def create_user(
    user_data: dict,
    current_user: AuthenticatedUser,
    db: DatabaseSession
):
    """Admin-only endpoint to create users"""
    # Only users with 'admin' permission can access this
    return {"message": "User created", "created_by": current_user["user_id"]}

@router.get("/service/health")
@require_role("service")
async def service_health(current_user: AuthenticatedUser):
    """Service-only health check"""
    return {"status": "healthy", "service": current_user["user_id"]}
```

## Testing

### Test Internal API Key

```python
import pytest
from fastapi.testclient import TestClient
from app import app

client = TestClient(app)

def test_internal_api_key_auth():
    response = client.get(
        "/protected-endpoint",
        headers={"X-API-Key": "your-secure-internal-api-key"}
    )
    assert response.status_code == 200
    assert response.json()["auth_type"] == "internal_api_key"
```

### Test Firebase Auth

```python
def test_firebase_auth():
    # Assuming you have a valid Firebase token
    firebase_token = "valid-firebase-id-token"
    
    response = client.get(
        "/protected-endpoint",
        headers={"Authorization": f"Bearer {firebase_token}"}
    )
    assert response.status_code == 200
    assert response.json()["auth_type"] == "firebase"
```

### Test Unauthorized Access

```python
def test_unauthorized_access():
    response = client.get("/protected-endpoint")
    assert response.status_code == 401
    assert "Authentication required" in response.json()["detail"]
```

## Security Best Practices

1. **Keep API Keys Secret**: Store internal API keys securely and rotate them regularly
2. **Use HTTPS**: Always use HTTPS in production to protect tokens in transit
3. **Token Validation**: Firebase tokens are automatically validated for expiration and signature
4. **Permission Checks**: Use role and permission decorators for fine-grained access control
5. **Logging**: Authentication events are logged for security monitoring

## Migration from Existing Auth

If you're migrating from the existing admin auth system, you can gradually adopt the new middleware:

```python
# Old way (still supported)
from deps import AdminUser

@router.get("/admin/old")
async def old_admin_endpoint(admin_user: AdminUser):
    return {"message": "Old admin auth"}

# New way (recommended)
from deps import AuthenticatedUser
from middleware.auth_middleware import require_role

@router.get("/admin/new")
@require_role("service")  # or require_permission("admin")
async def new_admin_endpoint(current_user: AuthenticatedUser):
    return {"message": "New auth system"}
```

## Troubleshooting

### Firebase Not Working

1. Check if `FIREBASE_CREDENTIALS_PATH` is set correctly
2. Verify the service account JSON file exists and is readable
3. Check logs for Firebase initialization errors

### API Key Not Working

1. Verify `INTERNAL_API_KEY` environment variable is set
2. Ensure the header name is exactly `X-API-Key`
3. Check for typos in the API key value

### Permission Denied

1. Check the user's role and permissions in the auth response
2. Verify the required permission/role matches what's expected
3. Use optional auth first to debug the user object structure
