.PHONY: help dev test migrate seed clean build up down logs

help:
	@echo "Available commands:"
	@echo "  dev      - Run development server"
	@echo "  test     - Run tests"
	@echo "  migrate  - Run database migrations"
	@echo "  seed     - Seed database with demo data"
	@echo "  build    - Build Docker image"
	@echo "  up       - Start services with docker-compose"
	@echo "  down     - Stop services"
	@echo "  logs     - View logs"
	@echo "  clean    - Clean up containers and volumes"

dev:
	python3 -m uvicorn app:app --reload --host 0.0.0.0 --port 8000

test:
	python3 -m pytest tests/ -v

migrate:
	python3 -m alembic upgrade head

seed:
	python3 seed_data.py

build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

logs:
	docker-compose logs -f

clean:
	docker-compose down -v
	docker system prune -f

install:
	pip install -r requirements.txt

format:
	black .
	isort .

lint:
	flake8 .
	mypy .

setup-dev: install migrate seed
	@echo "Development environment setup complete!"

docker-dev: build up
	@echo "Docker development environment started!"
	@echo "API available at: http://localhost:8000"
	@echo "Admin UI available at: http://localhost:8000/admin"
