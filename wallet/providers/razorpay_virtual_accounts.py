import httpx
import hmac
import hashlib
import base64
from typing import Dict, Any, Optional
from decimal import Decimal

from settings import settings


class RazorpayVirtualAccountsProvider:
    def __init__(self):
        self.base_url = settings.razorpay_base
        self.key_id = settings.razorpay_key_id
        self.key_secret = settings.razorpay_key_secret
        self.webhook_secret = settings.razorpay_webhook_secret

    def _get_auth_header(self) -> str:
        """Generate Basic Auth header for Razorpay API"""
        credentials = f"{self.key_id}:{self.key_secret}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"

    async def create_virtual_account(self, user_id: str, description: Optional[str] = None) -> Dict[str, Any]:
        """Create a virtual account for a user"""
        if not self.key_id or not self.key_secret:
            # Return mock response for development
            return {
                "id": f"va_mock_{user_id}",
                "name": f"Virtual Account for {user_id}",
                "status": "active",
                "receivers": [{
                    "id": f"ba_mock_{user_id}",
                    "entity": "bank_account",
                    "ifsc": "RAZR0000001",
                    "bank_name": "RazorpayX",
                    "name": f"Virtual Account for {user_id}",
                    "account": f"2323230{user_id[-6:]}"
                }]
            }

        url = f"{self.base_url}/virtual_accounts"
        headers = {
            "Authorization": self._get_auth_header(),
            "Content-Type": "application/json"
        }
        
        payload = {
            "receivers": {
                "types": ["bank_account"]
            },
            "description": description or f"Virtual Account for user {user_id}",
            "customer_id": user_id,
            "notes": {
                "user_id": user_id
            }
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()

    def parse_virtual_account_credited_event(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Parse virtual_account.credited webhook event"""
        payment = payload.get("payload", {}).get("payment", {}).get("entity", {})
        virtual_account = payload.get("payload", {}).get("virtual_account", {}).get("entity", {})
        
        return {
            "payment_id": payment.get("id"),
            "virtual_account_id": virtual_account.get("id"),
            "amount": Decimal(str(payment.get("amount", 0))) / 100,  # Convert paise to rupees
            "fee": Decimal(str(payment.get("fee", 0))) / 100,
            "tax": Decimal(str(payment.get("tax", 0))) / 100,
            "status": payment.get("status"),
            "method": payment.get("method"),
            "notes": payment.get("notes", {}),
            "user_id": virtual_account.get("notes", {}).get("user_id"),
            "customer_id": payment.get("customer_id"),
            "customer_name": payment.get("customer", {}).get("name") if payment.get("customer") else None
        }
