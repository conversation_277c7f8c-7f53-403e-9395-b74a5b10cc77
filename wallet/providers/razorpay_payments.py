import httpx
import hmac
import hashlib
import base64
import decimal
from typing import Dict, Any, Optional
from decimal import Decimal
import json

from settings import settings


class RazorpayPaymentsProvider:
    def __init__(self):
        self.base_url = settings.razorpay_base
        self.key_id = settings.razorpay_key_id
        self.key_secret = settings.razorpay_key_secret
        self.webhook_secret = settings.razorpay_webhook_secret

    def _get_auth_header(self) -> str:
        """Generate Basic Auth header for Razorpay API"""
        credentials = f"{self.key_id}:{self.key_secret}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"

    async def create_order(
        self, 
        amount_paise: int, 
        receipt: Optional[str] = None, 
        notes: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Create a Razorpay order"""
        if not self.key_id or not self.key_secret:
            # Return mock response for development
            return {
                "id": f"order_mock_{receipt or 'test'}",
                "amount": amount_paise,
                "currency": "INR",
                "receipt": receipt,
                "status": "created"
            }

        url = f"{self.base_url}/orders"
        headers = {
            "Authorization": self._get_auth_header(),
            "Content-Type": "application/json"
        }
        
        payload = {
            "amount": amount_paise,
            "currency": "INR",
            "receipt": receipt,
            "notes": notes or {}
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()

    def verify_webhook_signature(self, payload: bytes, signature: str) -> bool:
        """Verify Razorpay webhook signature"""
        if not self.webhook_secret:
            return True  # Skip verification in development

        expected_signature = hmac.new(
            self.webhook_secret.encode(),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(signature, expected_signature)

    def parse_payment_captured_event(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Parse payment.captured webhook event"""
        payment = payload.get("payload", {}).get("payment", {}).get("entity", {})
        
        # Helper function to safely convert to Decimal
        def safe_decimal(value, default=0):
            try:
                if value is None:
                    return Decimal(str(default))
                return Decimal(str(value)) / 100  # Convert paise to rupees
            except (ValueError, TypeError, decimal.InvalidOperation):
                return Decimal(str(default))
        
        return {
            "payment_id": payment.get("id"),
            "order_id": payment.get("order_id"),
            "amount": safe_decimal(payment.get("amount", 0)),
            "fee": safe_decimal(payment.get("fee", 0)),
            "tax": safe_decimal(payment.get("tax", 0)),
            "status": payment.get("status"),
            "method": payment.get("method"),
            "captured": payment.get("captured", False),
            "notes": payment.get("notes", {}),
            "customer_id": payment.get("customer_id"),
            "customer_name": payment.get("customer", {}).get("name") if payment.get("customer") else None
        }
