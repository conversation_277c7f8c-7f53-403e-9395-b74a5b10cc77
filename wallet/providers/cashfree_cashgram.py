import httpx
import hmac
import hashlib
from typing import Dict, Any, Optional
from decimal import Decimal
import time

from settings import settings


class CashfreeCashgramProvider:
    def __init__(self):
        self.base_url = settings.cashfree_base
        self.client_id = settings.cashfree_client_id
        self.client_secret = settings.cashfree_client_secret
        self.webhook_secret = settings.cashfree_webhook_secret

    def _get_headers(self) -> Dict[str, str]:
        """Generate headers for Cashfree API"""
        return {
            "X-Client-Id": self.client_id,
            "X-Client-Secret": self.client_secret,
            "Content-Type": "application/json"
        }

    async def create_cashgram(
        self,
        cashgram_id: str,
        amount_rupees: Decimal,
        phone: Optional[str] = None,
        email: Optional[str] = None,
        purpose: str = "Wallet Withdrawal",
        expires_in_days: int = 7
    ) -> Dict[str, Any]:
        """Create a Cashfree Cashgram"""
        if not self.client_id or not self.client_secret:
            # Return mock response for development
            return {
                "cashgramId": cashgram_id,
                "referenceId": cashgram_id,
                "status": "SUCCESS",
                "link": f"https://cashgram.cashfree.com/mock/{cashgram_id}",
                "amount": float(amount_rupees),
                "expiryDate": int(time.time()) + (expires_in_days * 24 * 60 * 60)
            }

        url = f"{self.base_url}/payout/v1/cashgram"
        headers = self._get_headers()
        
        payload = {
            "cashgramId": cashgram_id,
            "amount": float(amount_rupees),
            "name": "Wallet User",
            "email": email,
            "phone": phone,
            "linkPurpose": purpose,
            "linkExpiry": expires_in_days * 24 * 60 * 60,  # Convert days to seconds
            "remarks": f"Withdrawal for cashgram {cashgram_id}",
            "notifyCustomer": 1 if email or phone else 0
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()

    def verify_webhook_signature(self, payload: bytes, signature: str, timestamp: str) -> bool:
        """Verify Cashfree webhook signature"""
        if not self.webhook_secret:
            return True  # Skip verification in development

        # Cashfree webhook signature verification
        raw_body = payload.decode('utf-8')
        expected_signature = hmac.new(
            self.webhook_secret.encode(),
            f"{timestamp}.{raw_body}".encode(),
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(signature, expected_signature)

    def parse_cashgram_webhook_event(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Cashfree Cashgram webhook event"""
        data = payload.get("data", {})
        
        return {
            "cashgram_id": data.get("cashgramId"),
            "reference_id": data.get("referenceId"),
            "status": data.get("status"),  # SUCCESS, FAILED, EXPIRED, etc.
            "amount": Decimal(str(data.get("amount", 0))),
            "claimed_amount": Decimal(str(data.get("claimedAmount", 0))),
            "event_time": data.get("eventTime"),
            "utr": data.get("utr"),  # Unique Transaction Reference
            "remarks": data.get("remarks")
        }
