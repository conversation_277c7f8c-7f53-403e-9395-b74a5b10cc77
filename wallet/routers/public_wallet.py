from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.templating import <PERSON><PERSON>2Templates
from typing import Dict, Any
from pydantic import BaseModel
import hmac
import hashlib

from settings import settings
from deps import OptionalAuthenticatedUser, AuthenticatedUser

router = APIRouter(prefix="/wallet", tags=["wallet"])
templates = Jinja2Templates(directory="templates")


@router.get("/", response_class=HTMLResponse)
async def wallet_operations(request: Request, current_user: OptionalAuthenticatedUser):
    """Wallet operations page for topup and withdraw"""
    return templates.TemplateResponse("wallet.html", {"request": request})


@router.get("/config")
async def get_wallet_config() -> Dict[str, Any]:
    """Get wallet configuration for frontend"""
    return {
        "razorpay_key_id": settings.razorpay_key_id or "rzp_test_11111111111111",  # Fallback for demo
        "service_name": "Wallet Service"
    }


class PaymentVerificationRequest(BaseModel):
    razorpay_order_id: str
    razorpay_payment_id: str
    razorpay_signature: str


@router.post("/verify-payment")
async def verify_payment(request: PaymentVerificationRequest, current_user: AuthenticatedUser) -> Dict[str, Any]:
    """Verify Razorpay payment signature"""
    try:
        # Create signature string
        signature_string = f"{request.razorpay_order_id}|{request.razorpay_payment_id}"
        
        # Generate expected signature
        if settings.razorpay_key_secret:
            expected_signature = hmac.new(
                settings.razorpay_key_secret.encode(),
                signature_string.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # Verify signature
            if not hmac.compare_digest(expected_signature, request.razorpay_signature):
                raise HTTPException(status_code=400, detail="Invalid payment signature")
        
        return {
            "status": "success",
            "message": "Payment verified successfully",
            "payment_id": request.razorpay_payment_id,
            "order_id": request.razorpay_order_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Payment verification failed: {str(e)}")
