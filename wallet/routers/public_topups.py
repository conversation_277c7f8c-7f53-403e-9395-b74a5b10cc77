from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from deps import DatabaseSession, AuthenticatedUser
from schemas.topups import TopupOrderRequest, TopupOrderResponse, VirtualAccountRequest, VirtualAccountResponse
from schemas.common import SuccessResponse, ErrorResponse
from services.payment_service import PaymentService

router = APIRouter(prefix="/api/topups", tags=["topups"])


@router.post("/order", response_model=SuccessResponse)
async def create_topup_order(
    request: TopupOrderRequest,
    current_user: AuthenticatedUser,
    db: DatabaseSession
) -> Dict[str, Any]:
    """Create a Razorpay order for user topup"""
    try:
        payment_service = PaymentService(db)
        order_data = await payment_service.create_razorpay_order(
            user_id=request.user_id,
            amount=request.amount,
            receipt=request.receipt
        )
        
        response = TopupOrderResponse(**order_data)
        return SuccessResponse(data=response.dict())
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/virtual-account", response_model=SuccessResponse)
async def create_virtual_account(
    request: VirtualAccountRequest,
    current_user: AuthenticatedUser,
    db: DatabaseSession
) -> Dict[str, Any]:
    """Create or get virtual account for a user"""
    try:
        payment_service = PaymentService(db)
        va_data = await payment_service.create_virtual_account(request.user_id)
        
        response = VirtualAccountResponse(**va_data)
        return SuccessResponse(data=response.dict())
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")
