from fastapi import APIRouter, Request, Form, HTTPException, status
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from typing import Annotated

from services.auth_service import authenticate_user, create_session_token
from deps import OptionalAdminUser

router = APIRouter(prefix="/admin", tags=["admin-auth"])
templates = Jinja2Templates(directory="templates")


@router.get("/login", response_class=HTMLResponse)
async def login_page(request: Request, admin: OptionalAdminUser = None):
    """Display login page"""
    # If already authenticated, redirect to dashboard
    if admin:
        return RedirectResponse(url="/admin", status_code=302)
    
    return templates.TemplateResponse(
        "login.html", 
        {"request": request}
    )


@router.post("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    username: Annotated[str, Form()],
    password: Annotated[str, Form()]
):
    """Process login form"""
    # Authenticate user
    user = authenticate_user(username, password)
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {
                "request": request,
                "error": "Invalid username or password",
                "username": username
            }
        )
    
    # Create session token
    session_token = create_session_token(username)
    
    # Redirect to admin dashboard with session cookie
    response = RedirectResponse(url="/admin", status_code=302)
    response.set_cookie(
        key="session_token",
        value=session_token,
        max_age=28800,  # 8 hours
        httponly=True,
        secure=False,  # Set to True in production with HTTPS
        samesite="lax"
    )
    
    return response


@router.post("/logout")
async def logout():
    """Logout and clear session"""
    response = RedirectResponse(url="/admin/login", status_code=302)
    response.delete_cookie(key="session_token")
    return response


@router.get("/logout")
async def logout_get():
    """Logout via GET request"""
    response = RedirectResponse(url="/admin/login", status_code=302)
    response.delete_cookie(key="session_token")
    return response
