from fastapi import APIRouter, HTTPException, Request, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import json
import logging

from deps import DatabaseSession
from services.payment_service import PaymentService
from providers.razorpay_payments import RazorpayPaymentsProvider
from providers.razorpay_virtual_accounts import RazorpayVirtualAccountsProvider

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/webhooks", tags=["webhooks"])


@router.post("/razorpay")
async def razorpay_webhook(
    request: Request,
    db: DatabaseSession
):
    """Handle Razorpay webhooks"""
    try:
        # Get raw body and headers
        body = await request.body()
        signature = request.headers.get("X-Razorpay-Signature", "")
        
        # Parse JSON payload
        try:
            payload = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        
        event_type = payload.get("event")
        if not event_type:
            raise HTTPException(status_code=400, detail="Missing event type")
        
        logger.info(f"Received Razorpay webhook: {event_type}")
        # Verify signature
        razorpay_payments = RazorpayPaymentsProvider()
        if not razorpay_payments.verify_webhook_signature(body, signature):
            logger.warning(f"Invalid Razorpay webhook signature for event: {event_type}")
            raise HTTPException(status_code=401, detail="Invalid signature")
        
        # Process based on event type
        payment_service = PaymentService(db)
        
        if event_type == "payment.captured":
            success = await payment_service.process_razorpay_payment_captured(payload)
            if success:
                logger.info(f"Successfully processed payment.captured webhook")
            else:
                logger.warning(f"Payment not captured in webhook")

        if event_type == "order.paid":
            success = await payment_service.process_razorpay_payment_captured(payload)
            if success:
                logger.info(f"Successfully processed order.paid webhook")
            else:
                logger.warning(f"Order not paid in webhook")
        
        elif event_type == "virtual_account.credited":
            success = await payment_service.handle_virtual_account_credit(payload)
            if success:
                logger.info(f"Successfully processed virtual_account.credited webhook")
        
        else:
            logger.info(f"Unhandled Razorpay event type: {event_type}")
        
        return {"status": "success", "event": event_type}
        
    except HTTPException:
        raise
    except Exception as e:
        print(e)
        logger.error(f"Error processing Razorpay webhook: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
