from fastapi import APIRouter, HTTPException, Request, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import json
import logging

from deps import DatabaseSession
from services.payout_service import PayoutService
from providers.cashfree_cashgram import CashfreeCashgramProvider

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/webhooks", tags=["webhooks"])


@router.post("/cashfree")
async def cashfree_webhook(
    request: Request,
    db: DatabaseSession
):
    """Handle Cashfree webhooks"""
    try:
        # Get raw body and headers
        body = await request.body()
        signature = request.headers.get("x-webhook-signature", "")
        timestamp = request.headers.get("x-webhook-timestamp", "")
        
        # Parse JSON payload
        try:
            payload = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
        
        event_type = payload.get("type")
        if not event_type:
            raise HTTPException(status_code=400, detail="Missing event type")
        
        logger.info(f"Received Cashfree webhook: {event_type}")
        
        # Verify signature
        cashfree = CashfreeCashgramProvider()
        if not cashfree.verify_webhook_signature(body, signature, timestamp):
            logger.warning(f"Invalid Cashfree webhook signature for event: {event_type}")
            raise HTTPException(status_code=401, detail="Invalid signature")
        
        # Process Cashgram events
        payout_service = PayoutService(db)
        
        if event_type in ["CASHGRAM_SUCCESS", "CASHGRAM_CLAIM_SUCCESS", "CASHGRAM_FAILED", "CASHGRAM_EXPIRED"]:
            success = await payout_service.process_cashfree_webhook(payload)
            if success:
                logger.info(f"Successfully processed {event_type} webhook")
        else:
            logger.info(f"Unhandled Cashfree event type: {event_type}")
        
        return {"status": "success", "event": event_type}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing Cashfree webhook: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
