from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal

from deps import get_db, AuthenticatedUser
from services.wallet_entry_service import WalletEntryService, InsufficientBalanceError
from schemas.wallet_entry import (
    WalletEntryRequest, 
    WalletEntryResponse, 
    WalletBalanceResponse
)

router = APIRouter(prefix="/wallet-entry", tags=["Wallet Entry"])


@router.post("/{user_id}/add-entry", response_model=WalletEntryResponse)
async def add_wallet_entry(
    user_id: str,
    request: WalletEntryRequest,
    current_user: AuthenticatedUser,
    db: AsyncSession = Depends(get_db)
):
    """
    Add or subtract amount from user's wallet
    
    - **user_id**: User ID from URL path
    - **related_id**: Related transaction/order ID for reference
    - **order_id**: Optional order ID if related to an order
    - **wallet_amt**: Amount to add/subtract (positive for credit, negative for debit)
    - **entry_type**: Type of wallet entry (credit/debit/adjustment)
    - **description**: Optional description of the transaction
    - **reference_type**: Reference type for the transaction (default: manual_entry)
    - **idempotency_key**: Optional custom idempotency key to prevent duplicates
    
    For negative amounts (debits), the system validates that the user has sufficient balance.
    """
    try:
        # Override the user_id in request with the path parameter
        request.user_id = user_id
        wallet_service = WalletEntryService(db)
        result = await wallet_service.process_wallet_entry(request)
        return result
        
    except InsufficientBalanceError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process wallet entry: {str(e)}"
        )


@router.get("/{user_id}/balance", response_model=WalletBalanceResponse)
async def get_wallet_balance(
    user_id: str,
    current_user: AuthenticatedUser,
    db: AsyncSession = Depends(get_db)
):
    """
    Get current wallet balance for a user
    
    - **user_id**: User ID to get balance for
    """
    try:
        wallet_service = WalletEntryService(db)
        balance = await wallet_service.get_wallet_balance(user_id)
        
        return WalletBalanceResponse(
            user_id=user_id,
            current_balance=balance
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get wallet balance: {str(e)}"
        )
