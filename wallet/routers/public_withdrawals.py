from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from deps import DatabaseSession, AuthenticatedUser
from schemas.withdrawals import WithdrawalRequest, WithdrawalResponse
from schemas.common import SuccessResponse, ErrorResponse
from services.payout_service import PayoutService

router = APIRouter(prefix="/api/withdrawals", tags=["withdrawals"])


@router.post("/", response_model=SuccessResponse)
async def create_withdrawal(
    request: WithdrawalRequest,
    current_user: AuthenticatedUser,
    db: DatabaseSession
) -> Dict[str, Any]:
    """Create a Cashfree Cashgram for user withdrawal"""
    try:
        payout_service = PayoutService(db)
        withdrawal_data = await payout_service.create_cashgram(
            user_id=request.user_id,
            amount=request.amount,
            phone=request.phone,
            email=request.email
        )
        
        response = WithdrawalResponse(**withdrawal_data)
        return SuccessResponse(data=response.dict())
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")
