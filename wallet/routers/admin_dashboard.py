from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from deps import DatabaseSession, AdminUser
from models import User, Payment, Payout, JournalEntry, LedgerAccount
from services.ledger_service import LedgerService
from services.reconciliation_service import ReconciliationService
from schemas.common import PaginationParams

router = APIRouter(prefix="/admin", tags=["admin"])
templates = Jinja2Templates(directory="templates")


@router.get("/", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    db: DatabaseSession,
    admin: AdminUser
):
    """Admin dashboard with KPI cards"""
    try:
        ledger_service = LedgerService(db)
        
        # Get KPIs
        total_wallet_float = await ledger_service.get_total_wallet_float()
        
        # Count users
        user_count_stmt = select(func.count(User.id))
        user_count_result = await db.execute(user_count_stmt)
        total_users = user_count_result.scalar() or 0
        
        # Count payments (last 30 days)
        thirty_days_ago = datetime.now() - timedelta(days=30)
        payment_count_stmt = select(func.count(Payment.id)).where(
            Payment.created_at >= thirty_days_ago,
            Payment.status == 'captured'
        )
        payment_count_result = await db.execute(payment_count_stmt)
        topups_30d = payment_count_result.scalar() or 0
        
        # Count payouts (last 30 days)
        payout_count_stmt = select(func.count(Payout.id)).where(
            Payout.created_at >= thirty_days_ago,
            Payout.status == 'success'
        )
        payout_count_result = await db.execute(payout_count_stmt)
        withdrawals_30d = payout_count_result.scalar() or 0
        
        context = {
            "request": request,
            "total_wallet_float": float(total_wallet_float),
            "total_users": total_users,
            "topups_30d": topups_30d,
            "withdrawals_30d": withdrawals_30d
        }
        
        return templates.TemplateResponse("dashboard.html", context)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/users", response_class=HTMLResponse)
async def admin_users(
    request: Request,
    db: DatabaseSession,
    admin: AdminUser,
    page: int = 1,
    limit: int = 20,
    search: Optional[str] = None
):
    """Admin users page with search and pagination"""
    try:
        offset = (page - 1) * limit
        
        # Build query
        stmt = select(User).order_by(User.created_at.desc())
        if search:
            stmt = stmt.where(User.id.ilike(f"%{search}%"))
        
        stmt = stmt.limit(limit).offset(offset)
        result = await db.execute(stmt)
        users = result.scalars().all()
        
        # Get user balances
        ledger_service = LedgerService(db)
        user_balances = {}
        for user in users:
            balance = await ledger_service.get_user_balance(user.id)
            user_balances[user.id] = float(balance)
        
        context = {
            "request": request,
            "users": users,
            "user_balances": user_balances,
            "page": page,
            "search": search or ""
        }
        
        return templates.TemplateResponse("users.html", context)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/balances", response_class=HTMLResponse)
async def admin_balances(
    request: Request,
    db: DatabaseSession,
    admin: AdminUser
):
    """Admin balances overview"""
    try:
        ledger_service = LedgerService(db)
        reconciliation_service = ReconciliationService(db)
        
        # Get wallet float summary
        float_summary = await reconciliation_service.get_wallet_float_summary()
        
        # Get all user balances
        user_balances = await ledger_service.get_all_user_balances()
        
        context = {
            "request": request,
            "float_summary": float_summary,
            "user_balances": user_balances
        }
        
        return templates.TemplateResponse("balances.html", context)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/journal", response_class=HTMLResponse)
async def admin_journal(
    request: Request,
    db: DatabaseSession,
    admin: AdminUser,
    page: int = 1,
    limit: int = 20,
    user_id: Optional[str] = None
):
    """Admin journal entries with pagination"""
    try:
        offset = (page - 1) * limit
        
        ledger_service = LedgerService(db)
        journal_entries = await ledger_service.get_journal_entries(
            limit=limit,
            offset=offset,
            user_id=user_id
        )
        
        context = {
            "request": request,
            "journal_entries": journal_entries,
            "page": page,
            "user_id": user_id or ""
        }
        
        return templates.TemplateResponse("journal.html", context)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/payments", response_class=HTMLResponse)
async def admin_payments(
    request: Request,
    db: DatabaseSession,
    admin: AdminUser,
    page: int = 1,
    limit: int = 20,
    status: Optional[str] = None
):
    """Admin payments with filtering"""
    try:
        offset = (page - 1) * limit
        
        stmt = select(Payment).order_by(Payment.created_at.desc())
        if status:
            stmt = stmt.where(Payment.status == status)
        
        stmt = stmt.limit(limit).offset(offset)
        result = await db.execute(stmt)
        payments = result.scalars().all()
        
        context = {
            "request": request,
            "payments": payments,
            "page": page,
            "status_filter": status or ""
        }
        
        return templates.TemplateResponse("payments.html", context)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/payouts", response_class=HTMLResponse)
async def admin_payouts(
    request: Request,
    db: DatabaseSession,
    admin: AdminUser,
    page: int = 1,
    limit: int = 20,
    status: Optional[str] = None
):
    """Admin payouts with filtering"""
    try:
        offset = (page - 1) * limit
        
        stmt = select(Payout).order_by(Payout.created_at.desc())
        if status:
            stmt = stmt.where(Payout.status == status)
        
        stmt = stmt.limit(limit).offset(offset)
        result = await db.execute(stmt)
        payouts = result.scalars().all()
        
        context = {
            "request": request,
            "payouts": payouts,
            "page": page,
            "status_filter": status or ""
        }
        
        return templates.TemplateResponse("payouts.html", context)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")


# API endpoints for AJAX calls
@router.get("/api/dashboard-stats")
async def get_dashboard_stats(
    db: DatabaseSession,
    admin: AdminUser
) -> Dict[str, Any]:
    """Get dashboard statistics as JSON"""
    try:
        ledger_service = LedgerService(db)
        reconciliation_service = ReconciliationService(db)
        
        # Get wallet float summary
        float_summary = await reconciliation_service.get_wallet_float_summary()
        
        # Get recent activity counts
        thirty_days_ago = datetime.now() - timedelta(days=30)
        
        payment_count_stmt = select(func.count(Payment.id)).where(
            Payment.created_at >= thirty_days_ago,
            Payment.status == 'captured'
        )
        payment_count_result = await db.execute(payment_count_stmt)
        topups_30d = payment_count_result.scalar() or 0
        
        payout_count_stmt = select(func.count(Payout.id)).where(
            Payout.created_at >= thirty_days_ago,
            Payout.status == 'success'
        )
        payout_count_result = await db.execute(payout_count_stmt)
        withdrawals_30d = payout_count_result.scalar() or 0
        
        return {
            "wallet_float": float_summary,
            "topups_30d": topups_30d,
            "withdrawals_30d": withdrawals_30d
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")
