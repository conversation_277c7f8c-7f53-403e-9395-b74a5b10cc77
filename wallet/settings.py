from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # Database
    db_url: str = "sqlite+aiosqlite:///./wallet.db"
    
    # Razorpay
    razorpay_key_id: str = ""
    razorpay_key_secret: str = ""
    razorpay_webhook_secret: str = ""
    razorpay_base: str = "https://api.razorpay.com/v1"
    
    # Cashfree
    cashfree_client_id: str = ""
    cashfree_client_secret: str = ""
    cashfree_webhook_secret: str = ""
    cashfree_base: str = "https://payout-api.cashfree.com"
    
    # Service
    admin_api_key: str = "supersecretadminkey"
    service_idempotency_salt: str = "local_salt_for_dedup"
    
    # Authentication
    internal_api_key: str = "internal-service-key"
    firebase_credentials_path: Optional[str] = None  # Path to Firebase service account JSON
    
    # Development
    debug: bool = False
    log_level: str = "INFO"
    
    # Sentry
    sentry_dsn: str = "https://<EMAIL>/9"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()
