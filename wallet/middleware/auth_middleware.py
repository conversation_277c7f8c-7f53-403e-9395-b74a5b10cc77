"""
Authentication middleware supporting both internal API key and Firebase auth token
"""
import logging
from typing import Optional, Dict, Any
from fastapi import HTT<PERSON>Exception, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import firebase_admin
from firebase_admin import auth, credentials
from settings import settings

logger = logging.getLogger(__name__)

# Initialize Firebase Admin SDK
_firebase_app = None

def initialize_firebase():
    """Initialize Firebase Admin SDK if credentials are provided"""
    global _firebase_app
    if _firebase_app is None and settings.firebase_credentials_path:
        try:
            cred = credentials.Certificate(settings.firebase_credentials_path)
            _firebase_app = firebase_admin.initialize_app(cred)
            logger.info("Firebase Admin SDK initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Firebase Admin SDK: {e}")
            _firebase_app = False  # Mark as failed to avoid retrying
    return _firebase_app


class AuthenticationError(HTTPException):
    """Custom exception for authentication errors"""
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class AuthMiddleware:
    """Authentication middleware supporting internal API key and Firebase auth"""
    
    def __init__(self):
        self.security = HTTPBearer(auto_error=False)
    
    async def verify_internal_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Verify internal API key"""
        if api_key == settings.internal_api_key:
            return {
                "auth_type": "internal_api_key",
                "user_id": "internal_service",
                "role": "service",
                "permissions": ["read", "write", "admin"]
            }
        return None
    
    async def verify_firebase_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify Firebase auth token"""
        firebase_app = initialize_firebase()
        if not firebase_app:
            logger.warning("Firebase not initialized, skipping Firebase token verification")
            return None
        
        try:
            # Verify the Firebase ID token
            decoded_token = auth.verify_id_token(token)
            
            # Extract user information
            user_id = decoded_token.get('uid')
            email = decoded_token.get('email')
            email_verified = decoded_token.get('email_verified', False)
            
            return {
                "auth_type": "firebase",
                "user_id": user_id,
                "email": email,
                "email_verified": email_verified,
                "role": "user",
                "permissions": ["read", "write"],
                "firebase_claims": decoded_token
            }
        except auth.InvalidIdTokenError:
            logger.warning("Invalid Firebase ID token")
            return None
        except auth.ExpiredIdTokenError:
            logger.warning("Expired Firebase ID token")
            return None
        except Exception as e:
            logger.error(f"Firebase token verification error: {e}")
            return None
    
    async def authenticate_request(self, request: Request) -> Dict[str, Any]:
        """
        Main authentication method that checks for either internal API key or Firebase token
        
        Authentication priority:
        1. X-API-Key header (internal API key)
        2. Authorization Bearer token (Firebase auth token)
        """
        
        # Check for internal API key in X-API-Key header
        api_key = request.headers.get("X-API-Key")
        if api_key:
            auth_result = await self.verify_internal_api_key(api_key)
            if auth_result:
                logger.info(f"Authenticated via internal API key: {auth_result['user_id']}")
                return auth_result
            else:
                raise AuthenticationError("Invalid internal API key")
        
        # Check for Firebase token in Authorization header
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
            auth_result = await self.verify_firebase_token(token)
            if auth_result:
                logger.info(f"Authenticated via Firebase token: {auth_result['user_id']}")
                return auth_result
            else:
                raise AuthenticationError("Invalid or expired Firebase token")
        
        # No valid authentication found
        raise AuthenticationError("No valid authentication provided. Use X-API-Key header or Authorization Bearer token")


# Global instance
auth_middleware = AuthMiddleware()


async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    FastAPI dependency to get current authenticated user
    
    Usage:
        @router.get("/protected")
        async def protected_endpoint(current_user: dict = Depends(get_current_user)):
            return {"message": f"Hello {current_user['user_id']}"}
    """
    return await auth_middleware.authenticate_request(request)


async def get_current_user_optional(request: Request) -> Optional[Dict[str, Any]]:
    """
    Optional authentication dependency - returns None if not authenticated
    
    Usage:
        @router.get("/optional-auth")
        async def optional_endpoint(current_user: Optional[dict] = Depends(get_current_user_optional)):
            if current_user:
                return {"message": f"Hello {current_user['user_id']}"}
            else:
                return {"message": "Hello anonymous user"}
    """
    try:
        return await auth_middleware.authenticate_request(request)
    except AuthenticationError:
        return None


def require_permission(permission: str):
    """
    Decorator to require specific permission
    
    Usage:
        @router.post("/admin-only")
        @require_permission("admin")
        async def admin_endpoint(current_user: dict = Depends(get_current_user)):
            return {"message": "Admin access granted"}
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract current_user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise AuthenticationError("Authentication required")
            
            user_permissions = current_user.get('permissions', [])
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission '{permission}' required"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_role(role: str):
    """
    Decorator to require specific role
    
    Usage:
        @router.post("/service-only")
        @require_role("service")
        async def service_endpoint(current_user: dict = Depends(get_current_user)):
            return {"message": "Service access granted"}
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract current_user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise AuthenticationError("Authentication required")
            
            user_role = current_user.get('role')
            if user_role != role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role '{role}' required"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
