#!/usr/bin/env python3
"""
Seed script to create demo data for the wallet service
"""
import asyncio
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from db import AsyncSessionLocal
from models import User, Payment, Payout
from services.ledger_service import LedgerService, Line
import uuid


async def create_demo_users(db: AsyncSession) -> list[User]:
    """Create demo users"""
    users = [
        User(id="alice_123", kyc_status="verified"),
        User(id="bob_456", kyc_status="verified"),
        User(id="charlie_789", kyc_status="pending"),
        User(id="diana_101", kyc_status="verified"),
        User(id="eve_202", kyc_status="rejected")
    ]
    
    for user in users:
        db.add(user)
    
    await db.commit()
    print(f"Created {len(users)} demo users")
    return users


async def create_demo_payments(db: AsyncSession, users: list[User]) -> list[Payment]:
    """Create demo payment records"""
    payments = [
        Payment(
            gateway="razorpay",
            order_id="order_demo_001",
            payment_id="pay_demo_001",
            status="captured",
            amount=Decimal("500.00"),
            fee=Decimal("11.80"),
            tax=Decimal("1.80"),
            user_id=users[0].id
        ),
        Payment(
            gateway="razorpay",
            order_id="order_demo_002",
            payment_id="pay_demo_002",
            status="captured",
            amount=Decimal("1000.00"),
            fee=Decimal("23.60"),
            tax=Decimal("3.60"),
            user_id=users[1].id
        ),
        Payment(
            gateway="razorpay_va",
            order_id="va_demo_001",
            payment_id="pay_va_demo_001",
            status="captured",
            amount=Decimal("750.00"),
            fee=Decimal("17.70"),
            tax=Decimal("2.70"),
            user_id=users[3].id
        ),
        Payment(
            gateway="razorpay",
            order_id="order_demo_003",
            status="created",
            amount=Decimal("250.00"),
            fee=Decimal("0.00"),
            tax=Decimal("0.00"),
            user_id=users[2].id
        )
    ]
    
    for payment in payments:
        db.add(payment)
    
    await db.commit()
    print(f"Created {len(payments)} demo payments")
    return payments


async def create_demo_payouts(db: AsyncSession, users: list[User]) -> list[Payout]:
    """Create demo payout records"""
    payouts = [
        Payout(
            provider="cashfree",
            link_id="cg_demo_001",
            status="success",
            amount=Decimal("200.00"),
            user_id=users[0].id
        ),
        Payout(
            provider="cashfree",
            link_id="cg_demo_002",
            status="created",
            amount=Decimal("150.00"),
            user_id=users[1].id
        ),
        Payout(
            provider="cashfree",
            link_id="cg_demo_003",
            status="expired",
            amount=Decimal("100.00"),
            user_id=users[3].id
        )
    ]
    
    for payout in payouts:
        db.add(payout)
    
    await db.commit()
    print(f"Created {len(payouts)} demo payouts")
    return payouts


async def create_demo_journal_entries(db: AsyncSession, users: list[User], payments: list[Payment], payouts: list[Payout]):
    """Create demo journal entries for realistic balances"""
    ledger_service = LedgerService(db)
    
    # Create core accounts
    core_accounts = await ledger_service.get_or_create_core_accounts()
    
    # Process captured payments
    for payment in payments:
        if payment.status == "captured":
            user_account = await ledger_service.get_or_create_user_balance_account(payment.user_id)
            net_amount = payment.amount - payment.fee
            
            # Settlement and fee collection
            lines = [
                # Settlement: escrow -> platform_cash
                Line(account_id=str(core_accounts["escrow_receivable"].id), credit=payment.amount),
                Line(account_id=str(core_accounts["platform_cash"].id), debit=payment.amount),
                
                # Fee collection: platform_cash -> fees
                Line(account_id=str(core_accounts["platform_cash"].id), credit=payment.fee),
                Line(account_id=str(core_accounts["fees"].id), debit=payment.fee),
                
                # User credit: platform_cash -> user_balance
                Line(account_id=str(core_accounts["platform_cash"].id), credit=net_amount),
                Line(account_id=str(user_account.id), debit=net_amount)
            ]
            
            await ledger_service.post_double_entry(
                lines=lines,
                ref_type="payment",
                ref_id=payment.payment_id or payment.order_id
            )
    
    # Process successful payouts
    for payout in payouts:
        if payout.status == "success":
            user_account = await ledger_service.get_or_create_user_balance_account(payout.user_id)
            
            # Withdrawal: user_balance -> platform_cash
            lines = [
                Line(account_id=str(user_account.id), credit=payout.amount),
                Line(account_id=str(core_accounts["platform_cash"].id), debit=payout.amount)
            ]
            
            await ledger_service.post_double_entry(
                lines=lines,
                ref_type="payout",
                ref_id=payout.link_id
            )
    
    print("Created demo journal entries")


async def print_summary(db: AsyncSession):
    """Print summary of seeded data"""
    ledger_service = LedgerService(db)
    
    print("\n" + "="*50)
    print("DEMO DATA SUMMARY")
    print("="*50)
    
    # Total wallet float
    total_float = await ledger_service.get_total_wallet_float()
    print(f"Total Wallet Float: ₹{total_float}")
    
    # User balances
    user_balances = await ledger_service.get_all_user_balances()
    print(f"\nUser Balances ({len(user_balances)} users):")
    for balance in user_balances:
        print(f"  {balance['user_id']}: ₹{balance['balance']}")
    
    # Platform balances
    core_accounts = await ledger_service.get_or_create_core_accounts()
    print(f"\nPlatform Account Balances:")
    for account_type, account in core_accounts.items():
        balance = await ledger_service.get_account_balance(str(account.id))
        print(f"  {account_type}: ₹{balance}")
    
    print("\n" + "="*50)
    print("ADMIN ACCESS")
    print("="*50)
    print("URL: http://localhost:8000/admin")
    print("API Key Header: X-API-Key: supersecretadminkey")
    print("\nAPI ENDPOINTS")
    print("="*50)
    print("Topup Order: POST /api/topups/order")
    print("Withdrawal: POST /api/withdrawals")
    print("Razorpay Webhook: POST /webhooks/razorpay")
    print("Cashfree Webhook: POST /webhooks/cashfree")
    print("="*50)


async def main():
    """Main seeding function"""
    print("Starting database seeding...")
    
    async with AsyncSessionLocal() as db:
        try:
            # Create demo data
            users = await create_demo_users(db)
            payments = await create_demo_payments(db, users)
            payouts = await create_demo_payouts(db, users)
            await create_demo_journal_entries(db, users, payments, payouts)
            
            # Print summary
            await print_summary(db)
            
            print("\n✅ Database seeding completed successfully!")
            
        except Exception as e:
            print(f"❌ Error during seeding: {e}")
            await db.rollback()
            raise


if __name__ == "__main__":
    asyncio.run(main())
