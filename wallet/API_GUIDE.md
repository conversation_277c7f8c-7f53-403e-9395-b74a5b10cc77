# Wallet Service API Guide

This guide provides comprehensive documentation for the Wallet Service API endpoints.

## Base URL
```
http://localhost:8000
```

## Authentication
Currently, the wallet entry endpoints do not require authentication. Ensure proper authentication is implemented before production use.

---

## Wallet Entry API

### Overview
The Wallet Entry API allows you to add or subtract amounts from user wallets with proper validation and double-entry bookkeeping.

### Endpoints

#### 1. Get Wallet Balance
**GET** `/wallet-entry/{user_id}/balance`

Get the current wallet balance for a user.

**Parameters:**
- `user_id` (path): User ID to get balance for

**Response:**
```json
{
  "user_id": "test_user_789",
  "current_balance": "1000.50"
}
```

**Example:**
```bash
curl -X GET "http://localhost:8000/wallet-entry/balance/test_user_789"
```

---

#### 2. Add/Subtract Wallet Amount
**POST** `/wallet-entry/{user_id}/add-entry`

Add or subtract amount from a user's wallet with validation.

**Request Body:**
```json
{
  "user_id": "string",           // Optional: User ID (overridden by URL path parameter)
  "related_id": "string",        // Required: Related transaction/order ID for reference
  "order_id": "string",          // Optional: Order ID if related to an order
  "wallet_amt": "decimal",       // Required: Amount (positive for credit, negative for debit)
  "entry_type": "string",        // Required: "credit", "debit", or "adjustment"
  "description": "string",       // Optional: Description of the transaction
  "reference_type": "string",    // Optional: Reference type (default: "manual_entry")
  "idempotency_key": "string"    // Optional: Custom idempotency key to prevent duplicates
}
```

**Response:**
```json
{
  "success": true,
  "message": "Wallet credited successfully",
  "user_id": "test_user_789",
  "amount_processed": "1000.50",
  "new_balance": "1000.50",
  "entry_id": "429ba27f-d2cb-467b-8b69-f787f3932b12",
  "transaction_reference": "manual_credit_credit_001"
}
```

**Validation Rules:**
- For negative amounts (debits), the system validates that the user has sufficient balance
- Wallet amount cannot be zero
- Entry type must match the amount sign (positive = credit, negative = debit)

---

## Testing Examples

### Complete Test Flow

#### 1. Check Initial Balance
```bash
curl -X GET "http://localhost:8000/wallet-entry/test_user_789/balance"
```
**Expected Response:**
```json
{"user_id":"test_user_789","current_balance":"0"}
```

#### 2. Add Money to Wallet (Credit)
```bash
curl -X POST "http://localhost:8000/wallet-entry/test_user_789/add-entry" \
  -H "Content-Type: application/json" \
  -d '{
    "related_id": "credit_001", 
    "wallet_amt": 1000.50,
    "entry_type": "credit",
    "description": "Adding money to wallet",
    "reference_type": "manual_credit"
  }'
```
**Expected Response:**
```json
{
  "success": true,
  "message": "Wallet credited successfully",
  "user_id": "test_user_789",
  "amount_processed": "1000.50",
  "new_balance": "1000.50",
  "entry_id": "...",
  "transaction_reference": "manual_credit_credit_001"
}
```

#### 3. Check Balance After Credit
```bash
curl -X GET "http://localhost:8000/wallet-entry/test_user_789/balance"
```
**Expected Response:**
```json
{"user_id":"test_user_789","current_balance":"1000.50"}
```

#### 4. Subtract Money from Wallet (Debit)
```bash
curl -X POST "http://localhost:8000/wallet-entry/test_user_789/add-entry" \
  -H "Content-Type: application/json" \
  -d '{
    "related_id": "debit_001",
    "wallet_amt": -250.25,
    "entry_type": "debit", 
    "description": "Withdrawing money from wallet",
    "reference_type": "manual_debit"
  }'
```
**Expected Response:**
```json
{
  "success": true,
  "message": "Wallet debited successfully",
  "user_id": "test_user_789",
  "amount_processed": "-250.25",
  "new_balance": "750.25",
  "entry_id": "...",
  "transaction_reference": "manual_debit_debit_001"
}
```

#### 5. Attempt Overdraft (Should Fail)
```bash
curl -X POST "http://localhost:8000/wallet-entry/test_user_789/add-entry" \
  -H "Content-Type: application/json" \
  -d '{
    "related_id": "debit_002",
    "wallet_amt": -2000.00,
    "entry_type": "debit",
    "description": "Attempting overdraft",
    "reference_type": "manual_debit"
  }'
```
**Expected Response (400 Bad Request):**
```json
{
  "detail": "Insufficient balance. Current: 750.25, Required: 2000.0"
}
```

#### 6. Final Balance Check
```bash
curl -X GET "http://localhost:8000/wallet-entry/test_user_789/balance"
```
**Expected Response:**
```json
{"user_id":"test_user_789","current_balance":"750.25"}
```

---

## Error Handling

### Common Error Responses

#### 400 Bad Request
- **Insufficient Balance**: When attempting to debit more than available balance
- **Invalid Amount**: When wallet amount is zero
- **Type Mismatch**: When entry type doesn't match amount sign

#### 500 Internal Server Error
- **Database Errors**: Connection issues or constraint violations
- **Service Errors**: Internal processing failures

### Error Response Format
```json
{
  "detail": "Error description message"
}
```

---

## Features

### Auto User Creation
- Users are automatically created if they don't exist when performing wallet operations
- New users start with `kyc_status="pending"`
- User balance ledger accounts are automatically created

### Double-Entry Bookkeeping
- All transactions use proper double-entry accounting
- User balance accounts are treated as assets (debit increases balance)
- Platform cash accounts maintain the counterpart entries

### Idempotency
- Custom idempotency keys can be provided to prevent duplicate transactions
- System generates automatic idempotency keys if not provided

### Balance Validation
- Debit operations validate sufficient balance before processing
- Prevents negative balances (overdrafts)

---

## Integration Notes

### Production Considerations
1. **Authentication**: Implement proper authentication before production use
2. **Rate Limiting**: Add rate limiting to prevent abuse
3. **Monitoring**: Set up monitoring for wallet operations
4. **Audit Trail**: All transactions are logged with full audit trail
5. **Reconciliation**: Regular balance reconciliation recommended

### Database Schema
The wallet system uses the following core tables:
- `users`: User information and KYC status
- `ledger_accounts`: Account definitions (user_balance, platform_cash, etc.)
- `journal_entries`: All financial transactions with double-entry bookkeeping
- `balance_snapshots`: Cached balance calculations for performance

---

## Support

For issues or questions regarding the Wallet Entry API, please refer to the application logs or contact the development team.
