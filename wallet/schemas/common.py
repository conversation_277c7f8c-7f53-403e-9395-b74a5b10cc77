from pydantic import BaseModel, Field
from typing import Optional, Any
from decimal import Decimal


class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    details: Optional[str] = None


class SuccessResponse(BaseModel):
    success: bool = True
    data: Any


class PaginationParams(BaseModel):
    page: int = Field(default=1, ge=1)
    limit: int = Field(default=20, ge=1, le=100)


class BalanceInfo(BaseModel):
    user_id: str
    balance: Decimal
    currency: str = "INR"


class JournalEntryResponse(BaseModel):
    id: str
    entry_id: str
    account_id: str
    debit: Decimal
    credit: Decimal
    currency: str
    ref_type: str
    ref_id: str
    created_at: str
    
    class Config:
        from_attributes = True
