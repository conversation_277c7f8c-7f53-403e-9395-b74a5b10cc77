from pydantic import BaseModel, <PERSON>, validator
from decimal import Decimal
from typing import Optional
from enum import Enum


class WalletEntryType(str, Enum):
    CREDIT = "credit"  # Add money to wallet
    DEBIT = "debit"    # Subtract money from wallet
    ADJUSTMENT = "adjustment"  # Manual adjustment


class WalletEntryRequest(BaseModel):
    user_id: Optional[str] = Field(None, description="User ID (overridden by URL path parameter)")
    related_id: str = Field(..., description="Related transaction/order ID for reference")
    order_id: Optional[str] = Field(None, description="Order ID if related to an order")
    wallet_amt: Decimal = Field(..., description="Amount to add/subtract from wallet (positive for credit, negative for debit)")
    entry_type: WalletEntryType = Field(..., description="Type of wallet entry")
    description: Optional[str] = Field(None, description="Description of the transaction")
    reference_type: str = Field(default="manual_entry", description="Reference type for the transaction")
    idempotency_key: Optional[str] = Field(None, description="Custom idempotency key to prevent duplicate entries")
    
    @validator('wallet_amt')
    def validate_wallet_amt(cls, v):
        if v == 0:
            raise ValueError("Wallet amount cannot be zero")
        return v
    
    @validator('entry_type', always=True)
    def validate_entry_type_with_amount(cls, v, values):
        wallet_amt = values.get('wallet_amt')
        if wallet_amt is not None:
            if wallet_amt > 0 and v == WalletEntryType.DEBIT:
                raise ValueError("Positive amount should use CREDIT entry type")
            elif wallet_amt < 0 and v == WalletEntryType.CREDIT:
                raise ValueError("Negative amount should use DEBIT entry type")
        return v


class WalletEntryResponse(BaseModel):
    success: bool
    message: str
    user_id: str
    amount_processed: Decimal
    new_balance: Decimal
    entry_id: str
    transaction_reference: str

    class Config:
        from_attributes = True


class WalletBalanceResponse(BaseModel):
    user_id: str
    current_balance: Decimal
    
    class Config:
        from_attributes = True
