from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from decimal import Decimal


class TopupOrderRequest(BaseModel):
    user_id: str = Field(..., description="User ID for the topup")
    amount: Decimal = Field(..., gt=0, description="Amount in INR")
    receipt: Optional[str] = Field(None, description="Optional receipt identifier")


class TopupOrderResponse(BaseModel):
    order_id: str
    amount: Decimal
    currency: str = "INR"
    receipt: Optional[str] = None
    razorpay_order: Dict[str, Any]


class VirtualAccountRequest(BaseModel):
    user_id: str = Field(..., description="User ID for virtual account")


class VirtualAccountResponse(BaseModel):
    virtual_account_id: str
    account_number: str
    ifsc: str
    user_id: str
    status: str
