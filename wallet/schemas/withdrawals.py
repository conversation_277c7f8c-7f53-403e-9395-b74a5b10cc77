from pydantic import BaseModel, Field
from typing import Optional
from decimal import Decimal


class WithdrawalRequest(BaseModel):
    user_id: str = Field(..., description="User ID for the withdrawal")
    amount: Decimal = Field(..., gt=0, description="Amount in INR")
    phone: Optional[str] = Field(None, description="Phone number for Cashgram")
    email: Optional[str] = Field(None, description="Email for Cashgram")


class WithdrawalResponse(BaseModel):
    payout_id: str
    link_id: str
    amount: Decimal
    status: str
    cashgram_link: str
