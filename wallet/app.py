from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
import logging
import uvicorn
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration

from routers import public_topups, public_withdrawals, public_wallet, webhooks_razorpay, webhooks_cashfree, admin_dashboard, admin_auth, wallet_entry
from settings import settings
from deps import AuthenticationRequired

# Initialize Sentry
if settings.sentry_dsn:
    sentry_sdk.init(
        dsn=settings.sentry_dsn,
        integrations=[
            FastApiIntegration(),
            SqlalchemyIntegration(),
        ],
        traces_sample_rate=0.1,  # 10% of transactions for performance monitoring
        profiles_sample_rate=0.1,  # 10% for profiling
        environment="production" if not settings.debug else "development",
        release="wallet-service@1.0.0",
    )

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Wallet Service",
    description="Production-ready wallet microservice with Razorpay & Cashfree integration",
    version="1.0.0",
    debug=settings.debug
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Add exception handler for authentication
@app.exception_handler(AuthenticationRequired)
async def auth_exception_handler(request: Request, exc: AuthenticationRequired):
    """Handle authentication required exceptions by redirecting to login"""
    # For web UI requests, redirect to login page
    if request.url.path.startswith("/admin") and "text/html" in request.headers.get("accept", ""):
        return RedirectResponse(url="/admin/login", status_code=302)
    # For API requests, return 401
    return {"detail": "Authentication required"}

# Include routers
app.include_router(public_topups.router)
app.include_router(public_withdrawals.router)
app.include_router(public_wallet.router)
app.include_router(wallet_entry.router)
app.include_router(webhooks_razorpay.router)
app.include_router(webhooks_cashfree.router)
app.include_router(admin_auth.router)  # Must be before admin_dashboard for login routes
app.include_router(admin_dashboard.router)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Wallet Service",
        "version": "1.0.0",
        "status": "healthy"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
