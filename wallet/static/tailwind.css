/* Custom styles for Wallet Service Admin */

/* Additional utility classes */
.btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition duration-200;
}

.btn-secondary {
    @apply bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md transition duration-200;
}

.btn-success {
    @apply bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md transition duration-200;
}

.btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md transition duration-200;
}

/* Status badges */
.status-created {
    @apply bg-yellow-100 text-yellow-800;
}

.status-captured, .status-success {
    @apply bg-green-100 text-green-800;
}

.status-failed {
    @apply bg-red-100 text-red-800;
}

.status-expired {
    @apply bg-gray-100 text-gray-800;
}

/* Table styles */
.admin-table {
    @apply min-w-full divide-y divide-gray-200;
}

.admin-table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.admin-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm;
}

/* Card styles */
.admin-card {
    @apply bg-white overflow-hidden shadow rounded-lg;
}

.admin-card-header {
    @apply px-4 py-5 sm:px-6 border-b border-gray-200;
}

.admin-card-body {
    @apply px-4 py-5 sm:p-6;
}
