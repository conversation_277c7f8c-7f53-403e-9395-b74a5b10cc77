{"info": {"_postman_id": "wallet-entry-api-collection", "name": "Wallet Entry API", "description": "Complete API collection for Wallet Entry operations including balance checks and wallet transactions", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Wallet Balance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/wallet-entry/{{user_id}}/balance", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{user_id}}", "balance"]}, "description": "Get the current wallet balance for a user. The user will be auto-created if they don't exist."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/wallet-entry/test_user_789/balance", "host": ["{{base_url}}"], "path": ["wallet-entry", "test_user_789", "balance"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"user_id\": \"test_user_789\",\n    \"current_balance\": \"0\"\n}"}]}, {"name": "Add Money to Wallet (Credit)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"credit_001\",\n    \"order_id\": \"order_123\",\n    \"wallet_amt\": 1000.50,\n    \"entry_type\": \"credit\",\n    \"description\": \"Adding money to wallet\",\n    \"reference_type\": \"manual_credit\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/{{user_id}}/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{user_id}}", "add-entry"]}, "description": "Add money to a user's wallet. Use positive amounts for credits."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"credit_001\",\n    \"wallet_amt\": 1000.50,\n    \"entry_type\": \"credit\",\n    \"description\": \"Adding money to wallet\",\n    \"reference_type\": \"manual_credit\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/test_user_789/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "test_user_789", "add-entry"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Wallet credited successfully\",\n    \"user_id\": \"test_user_789\",\n    \"amount_processed\": \"1000.50\",\n    \"new_balance\": \"1000.50\",\n    \"entry_id\": \"429ba27f-d2cb-467b-8b69-f787f3932b12\",\n    \"transaction_reference\": \"manual_credit_credit_001\"\n}"}]}, {"name": "Subtract Money from Wallet (Debit)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"debit_001\",\n    \"wallet_amt\": -250.25,\n    \"entry_type\": \"debit\",\n    \"description\": \"Withdrawing money from wallet\",\n    \"reference_type\": \"manual_debit\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/{{user_id}}/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{user_id}}", "add-entry"]}, "description": "Subtract money from a user's wallet. Use negative amounts for debits. The system will validate sufficient balance."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"debit_001\",\n    \"wallet_amt\": -250.25,\n    \"entry_type\": \"debit\",\n    \"description\": \"Withdrawing money from wallet\",\n    \"reference_type\": \"manual_debit\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/test_user_789/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "test_user_789", "add-entry"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Wallet debited successfully\",\n    \"user_id\": \"test_user_789\",\n    \"amount_processed\": \"-250.25\",\n    \"new_balance\": \"750.25\",\n    \"entry_id\": \"429ba27f-d2cb-467b-8b69-f787f3932b12\",\n    \"transaction_reference\": \"manual_debit_debit_001\"\n}"}, {"name": "Insufficient Balance Error", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"debit_002\",\n    \"wallet_amt\": -2000.00,\n    \"entry_type\": \"debit\",\n    \"description\": \"Attempting overdraft\",\n    \"reference_type\": \"manual_debit\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/test_user_789/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "test_user_789", "add-entry"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"detail\": \"Insufficient balance. Current: 750.25, Required: 2000.0\"\n}"}]}, {"name": "Wallet Adjustment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"adjustment_001\",\n    \"wallet_amt\": 100.00,\n    \"entry_type\": \"adjustment\",\n    \"description\": \"Manual wallet adjustment\",\n    \"reference_type\": \"admin_adjustment\",\n    \"idempotency_key\": \"adj_001_unique\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/{{user_id}}/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{user_id}}", "add-entry"]}, "description": "Make manual adjustments to wallet balance with custom idempotency key."}, "response": []}, {"name": "Test Complete Flow", "item": [{"name": "1. Check Initial Balance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/wallet-entry/{{test_user_id}}/balance", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{test_user_id}}", "balance"]}}, "response": []}, {"name": "2. Add Money (Credit)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"test_credit_001\",\n    \"wallet_amt\": 1000.50,\n    \"entry_type\": \"credit\",\n    \"description\": \"Test credit transaction\",\n    \"reference_type\": \"test_credit\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/{{test_user_id}}/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{test_user_id}}", "add-entry"]}}, "response": []}, {"name": "3. Check Balance After Credit", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/wallet-entry/{{test_user_id}}/balance", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{test_user_id}}", "balance"]}}, "response": []}, {"name": "4. Subtract Money (Debit)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"test_debit_001\",\n    \"wallet_amt\": -250.25,\n    \"entry_type\": \"debit\",\n    \"description\": \"Test debit transaction\",\n    \"reference_type\": \"test_debit\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/{{test_user_id}}/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{test_user_id}}", "add-entry"]}}, "response": []}, {"name": "5. <PERSON><PERSON>pt Overdraft (Should Fail)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"related_id\": \"test_overdraft_001\",\n    \"wallet_amt\": -2000.00,\n    \"entry_type\": \"debit\",\n    \"description\": \"Attempting overdraft\",\n    \"reference_type\": \"test_overdraft\"\n}"}, "url": {"raw": "{{base_url}}/wallet-entry/{{test_user_id}}/add-entry", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{test_user_id}}", "add-entry"]}}, "response": []}, {"name": "6. Final Balance Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/wallet-entry/{{test_user_id}}/balance", "host": ["{{base_url}}"], "path": ["wallet-entry", "{{test_user_id}}", "balance"]}}, "response": []}], "description": "Complete test flow demonstrating all wallet entry operations in sequence."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string", "description": "Base URL for the Wallet Service API"}, {"key": "user_id", "value": "test_user_789", "type": "string", "description": "User ID for wallet operations"}, {"key": "test_user_id", "value": "postman_test_user", "type": "string", "description": "Test user ID for complete flow testing"}]}