<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Wallet Service Admin{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <!-- Razorpay Checkout SDK -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">Wallet Service Admin</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/admin" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                    <a href="/admin/users" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Users</a>
                    <a href="/admin/balances" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Balances</a>
                    <a href="/admin/journal" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Journal</a>
                    <a href="/admin/payments" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Payments</a>
                    <a href="/admin/payouts" class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Payouts</a>
                    <div class="border-l border-gray-300 pl-4">
                        <a href="/admin/logout" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t mt-12">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-sm text-gray-500">
                Wallet Service Admin - Built with FastAPI & Tailwind CSS
            </p>
        </div>
    </footer>
</body>
</html>
