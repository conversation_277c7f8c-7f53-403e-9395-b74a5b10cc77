{% extends "layout.html" %}

{% block title %}Balances - Wallet Service Admin{% endblock %}

{% block content %}
<div class="px-4 py-6 sm:px-0">
    <div class="border-4 border-dashed border-gray-200 rounded-lg p-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Balance Overview</h1>
        
        <!-- Platform Balance Summary -->
        <div class="bg-white shadow rounded-lg p-6 mb-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Platform Balances</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">₹{{ "%.2f"|format(float_summary.total_user_balance) }}</div>
                    <div class="text-sm text-gray-500">Total User Balance</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">₹{{ "%.2f"|format(float_summary.platform_balances.get('platform_cash', 0)) }}</div>
                    <div class="text-sm text-gray-500">Platform Cash</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">₹{{ "%.2f"|format(float_summary.platform_balances.get('fees', 0)) }}</div>
                    <div class="text-sm text-gray-500">Fees Collected</div>
                </div>
            </div>
        </div>

        <!-- User Balances Table -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">User Balances</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">Individual user wallet balances</p>
            </div>
            <ul class="divide-y divide-gray-200">
                {% for balance in user_balances %}
                <li class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="text-sm font-medium text-gray-900">{{ balance.user_id }}</div>
                        <div class="text-sm font-medium text-gray-900">₹{{ "%.2f"|format(balance.balance) }}</div>
                    </div>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endblock %}
