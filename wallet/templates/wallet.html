{% extends "layout.html" %}

{% block title %}Wallet Operations{% endblock %}

{% block content %}
<div class="px-4 py-6 sm:px-0">
    <div class="border-4 border-dashed border-gray-200 rounded-lg p-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Wallet Operations</h1>
            <p class="text-gray-600">Manage your wallet balance with topups and withdrawals</p>
        </div>

        <!-- User ID Input -->
        <div class="max-w-md mx-auto mb-8">
            <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">User ID</label>
            <input type="text" id="user_id" name="user_id" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Enter your user ID" required>
        </div>

        <!-- Operations Grid -->
        <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            
            <!-- Topup Section -->
            <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <div class="flex items-center mb-4">
                    <div class="bg-green-100 p-2 rounded-full mr-3">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900">Add Money (Topup)</h2>
                </div>
                
                <form id="topup-form" class="space-y-4">
                    <div>
                        <label for="topup_amount" class="block text-sm font-medium text-gray-700 mb-1">Amount (INR)</label>
                        <input type="number" id="topup_amount" name="amount" min="1" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="Enter amount" required>
                    </div>
                    
                    <div>
                        <label for="receipt" class="block text-sm font-medium text-gray-700 mb-1">Receipt ID (Optional)</label>
                        <input type="text" id="receipt" name="receipt"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="Optional receipt identifier">
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                        Create Topup Order
                    </button>
                </form>
                
                <div id="topup-result" class="mt-4 hidden"></div>
            </div>

            <!-- Withdrawal Section -->
            <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <div class="flex items-center mb-4">
                    <div class="bg-red-100 p-2 rounded-full mr-3">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900">Withdraw Money</h2>
                </div>
                
                <form id="withdrawal-form" class="space-y-4">
                    <div>
                        <label for="withdrawal_amount" class="block text-sm font-medium text-gray-700 mb-1">Amount (INR)</label>
                        <input type="number" id="withdrawal_amount" name="amount" min="1" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                               placeholder="Enter amount" required>
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" id="phone" name="phone"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                               placeholder="Enter phone number">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <input type="email" id="email" name="email"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                               placeholder="Enter email address">
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        Create Withdrawal
                    </button>
                </form>
                
                <div id="withdrawal-result" class="mt-4 hidden"></div>
            </div>
        </div>

        <!-- Virtual Account Section -->
        <div class="max-w-2xl mx-auto mt-8">
            <div class="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <div class="flex items-center mb-4">
                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900">Virtual Account</h2>
                </div>
                
                <p class="text-gray-600 mb-4">Get your dedicated virtual account for direct bank transfers</p>
                
                <button id="create-va-btn" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Get Virtual Account
                </button>
                
                <div id="va-result" class="mt-4 hidden"></div>
            </div>
        </div>
    </div>
</div>

<script>
// Basic test to check if JavaScript is working
console.log('Wallet page JavaScript loaded successfully');

// Utility functions
function showResult(elementId, content, isError = false) {
    const element = document.getElementById(elementId);
    element.innerHTML = content;
    element.className = `mt-4 p-4 rounded-md ${isError ? 'bg-red-50 border border-red-200 text-red-700' : 'bg-green-50 border border-green-200 text-green-700'}`;
    element.classList.remove('hidden');
}

function hideResult(elementId) {
    document.getElementById(elementId).classList.add('hidden');
}

function getUserId() {
    const userId = document.getElementById('user_id').value.trim();
    if (!userId) {
        alert('Please enter a User ID');
        return null;
    }
    return userId;
}

// Topup form handler with Razorpay integration
document.getElementById('topup-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    console.log('Topup form submitted');
    
    const userId = getUserId();
    if (!userId) return;
    
    console.log('Processing topup for user:', userId);
    
    const formData = new FormData(e.target);
    const amount = parseFloat(formData.get('amount'));
    const receipt = formData.get('receipt');
    
    console.log('Amount:', amount, 'Receipt:', receipt);
    
    try {
        hideResult('topup-result');
        
        // Step 1: Create Razorpay order
        const response = await fetch('/api/topups/order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                amount: amount,
                receipt: receipt || null
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            const orderData = data.data;
            
            // Step 2: Get Razorpay config and initialize checkout
            const configResponse = await fetch('/wallet/config');
            const config = await configResponse.json();
            
            console.log('Razorpay Config:', config);
            console.log('Order Data:', orderData);
            
            // Wait for Razorpay SDK to load
            let razorpayLoadAttempts = 0;
            const maxAttempts = 10;
            
            const waitForRazorpay = () => {
                return new Promise((resolve, reject) => {
                    const checkRazorpay = () => {
                        if (typeof Razorpay !== 'undefined') {
                            resolve();
                        } else if (razorpayLoadAttempts < maxAttempts) {
                            razorpayLoadAttempts++;
                            setTimeout(checkRazorpay, 500);
                        } else {
                            reject(new Error('Razorpay SDK failed to load'));
                        }
                    };
                    checkRazorpay();
                });
            };
            
            try {
                await waitForRazorpay();
                console.log('Razorpay SDK loaded successfully');
            } catch (error) {
                showResult('topup-result', `
                    <h3 class="font-semibold mb-2 text-red-600">Razorpay SDK Not Loaded</h3>
                    <p>The Razorpay payment gateway is not available. Please refresh the page and try again.</p>
                    <p><strong>Error:</strong> ${error.message}</p>
                `, true);
                return;
            }
            
            const options = {
                key: config.razorpay_key_id,
                amount: orderData.amount * 100, // Amount in paise
                currency: orderData.currency,
                name: 'Wallet Service',
                description: 'Add money to wallet',
                order_id: orderData.order_id,
                handler: async function (response) {
                    // Payment successful - verify with backend
                    try {
                        const verifyResponse = await fetch('/wallet/verify-payment', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                razorpay_order_id: response.razorpay_order_id,
                                razorpay_payment_id: response.razorpay_payment_id,
                                razorpay_signature: response.razorpay_signature
                            })
                        });
                        
                        const verifyData = await verifyResponse.json();
                        
                        if (verifyResponse.ok) {
                            showResult('topup-result', `
                                <h3 class="font-semibold mb-2 text-green-600">Payment Successful! 🎉</h3>
                                <p><strong>Payment ID:</strong> ${response.razorpay_payment_id}</p>
                                <p><strong>Order ID:</strong> ${response.razorpay_order_id}</p>
                                <p><strong>Amount:</strong> ₹${orderData.amount}</p>
                                <p><strong>Status:</strong> ${verifyData.status}</p>
                                <div class="mt-3 p-3 bg-green-50 rounded border">
                                    <p class="text-sm text-green-700">✅ Payment verified and money has been added to your wallet successfully!</p>
                                </div>
                            `);
                        } else {
                            showResult('topup-result', `
                                <h3 class="font-semibold mb-2 text-yellow-600">Payment Completed but Verification Failed</h3>
                                <p><strong>Payment ID:</strong> ${response.razorpay_payment_id}</p>
                                <p><strong>Error:</strong> ${verifyData.detail}</p>
                                <div class="mt-3 p-3 bg-yellow-50 rounded border">
                                    <p class="text-sm text-yellow-700">Please contact support with your payment ID for assistance.</p>
                                </div>
                            `, false);
                        }
                    } catch (error) {
                        showResult('topup-result', `
                            <h3 class="font-semibold mb-2 text-yellow-600">Payment Completed but Verification Error</h3>
                            <p><strong>Payment ID:</strong> ${response.razorpay_payment_id}</p>
                            <p><strong>Error:</strong> ${error.message}</p>
                            <div class="mt-3 p-3 bg-yellow-50 rounded border">
                                <p class="text-sm text-yellow-700">Please contact support with your payment ID for assistance.</p>
                            </div>
                        `, false);
                    }
                    
                    // Reset form
                    document.getElementById('topup-form').reset();
                },
                prefill: {
                    name: userId,
                    email: '<EMAIL>',
                    contact: '9999999999'
                },
                notes: {
                    user_id: userId,
                    receipt: receipt || ''
                },
                theme: {
                    color: '#059669'
                },
                modal: {
                    ondismiss: function() {
                        showResult('topup-result', `
                            <h3 class="font-semibold mb-2 text-yellow-600">Payment Cancelled</h3>
                            <p>You can try again anytime.</p>
                            <p><strong>Order ID:</strong> ${orderData.order_id}</p>
                        `, false);
                    }
                }
            };
            
            const rzp = new Razorpay(options);
            
            rzp.on('payment.failed', function (response) {
                showResult('topup-result', `
                    <h3 class="font-semibold mb-2 text-red-600">Payment Failed</h3>
                    <p><strong>Error:</strong> ${response.error.description}</p>
                    <p><strong>Order ID:</strong> ${response.error.metadata.order_id}</p>
                    <p><strong>Payment ID:</strong> ${response.error.metadata.payment_id}</p>
                    <div class="mt-3 p-3 bg-red-50 rounded border">
                        <p class="text-sm text-red-700">Please try again or contact support if the issue persists.</p>
                    </div>
                `, true);
            });
            
            // Open Razorpay checkout with error handling
            try {
                console.log('Opening Razorpay checkout with options:', options);
                rzp.open();
                console.log('Razorpay checkout opened successfully');
            } catch (error) {
                console.error('Error opening Razorpay checkout:', error);
                showResult('topup-result', `
                    <h3 class="font-semibold mb-2 text-red-600">Checkout Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>Please try again or refresh the page.</p>
                    <div class="mt-3 p-3 bg-red-50 rounded border">
                        <p class="text-sm text-red-700">If the issue persists, please contact support.</p>
                    </div>
                `, true);
            }
            
        } else {
            showResult('topup-result', `<strong>Error:</strong> ${data.detail || 'Failed to create topup order'}`, true);
        }
    } catch (error) {
        showResult('topup-result', `<strong>Error:</strong> ${error.message}`, true);
    }
});

// Withdrawal form handler
document.getElementById('withdrawal-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const userId = getUserId();
    if (!userId) return;
    
    const formData = new FormData(e.target);
    const amount = parseFloat(formData.get('amount'));
    const phone = formData.get('phone');
    const email = formData.get('email');
    
    try {
        hideResult('withdrawal-result');
        
        const response = await fetch('/api/withdrawals/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                amount: amount,
                phone: phone || null,
                email: email || null
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            const withdrawalData = data.data;
            showResult('withdrawal-result', `
                <h3 class="font-semibold mb-2">Withdrawal Created Successfully!</h3>
                <p><strong>Payout ID:</strong> ${withdrawalData.payout_id}</p>
                <p><strong>Link ID:</strong> ${withdrawalData.link_id}</p>
                <p><strong>Amount:</strong> ₹${withdrawalData.amount}</p>
                <p><strong>Status:</strong> ${withdrawalData.status}</p>
                <div class="mt-3">
                    <a href="${withdrawalData.cashgram_link}" target="_blank" 
                       class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium">
                        Open Cashgram Link
                    </a>
                </div>
            `);
        } else {
            showResult('withdrawal-result', `<strong>Error:</strong> ${data.detail || 'Failed to create withdrawal'}`, true);
        }
    } catch (error) {
        showResult('withdrawal-result', `<strong>Error:</strong> ${error.message}`, true);
    }
});

// Virtual Account button handler
document.getElementById('create-va-btn').addEventListener('click', async () => {
    const userId = getUserId();
    if (!userId) return;
    
    try {
        hideResult('va-result');
        
        const response = await fetch('/api/topups/virtual-account', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            const vaData = data.data;
            showResult('va-result', `
                <h3 class="font-semibold mb-2">Virtual Account Details</h3>
                <div class="grid grid-cols-1 gap-2 text-sm">
                    <p><strong>Account Number:</strong> ${vaData.account_number}</p>
                    <p><strong>IFSC Code:</strong> ${vaData.ifsc}</p>
                    <p><strong>Status:</strong> ${vaData.status}</p>
                    <p><strong>Virtual Account ID:</strong> ${vaData.virtual_account_id}</p>
                </div>
                <div class="mt-3 p-3 bg-blue-50 rounded border">
                    <p class="text-sm text-blue-700">Use these details to transfer money directly to your wallet</p>
                </div>
            `);
        } else {
            showResult('va-result', `<strong>Error:</strong> ${data.detail || 'Failed to get virtual account'}`, true);
        }
    } catch (error) {
        showResult('va-result', `<strong>Error:</strong> ${error.message}`, true);
    }
});
</script>
{% endblock %}
