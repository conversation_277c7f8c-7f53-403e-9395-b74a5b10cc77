{% extends "layout.html" %}

{% block title %}Users - Wallet Service Admin{% endblock %}

{% block content %}
<div class="px-4 py-6 sm:px-0">
    <div class="border-4 border-dashed border-gray-200 rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-900">Users</h1>
            
            <!-- Search Form -->
            <form method="GET" class="flex items-center space-x-2">
                <input type="text" name="search" value="{{ search }}" placeholder="Search users..." 
                       class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600">
                    Search
                </button>
            </form>
        </div>

        <!-- Users Table -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for user in users %}
                <li class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700">{{ user.id[:2].upper() }}</span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ user.id }}</div>
                                <div class="text-sm text-gray-500">KYC: {{ user.kyc_status }}</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">₹{{ "%.2f"|format(user_balances.get(user.id, 0)) }}</div>
                                <div class="text-sm text-gray-500">Balance</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-500">{{ user.created_at.strftime('%Y-%m-%d') }}</div>
                                <div class="text-sm text-gray-500">Joined</div>
                            </div>
                        </div>
                    </div>
                </li>
                {% endfor %}
            </ul>
        </div>

        <!-- Pagination -->
        <div class="mt-6 flex justify-center">
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                {% if page > 1 %}
                <a href="?page={{ page - 1 }}&search={{ search }}" 
                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    Previous
                </a>
                {% endif %}
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                    Page {{ page }}
                </span>
                <a href="?page={{ page + 1 }}&search={{ search }}" 
                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    Next
                </a>
            </nav>
        </div>
    </div>
</div>
{% endblock %}
