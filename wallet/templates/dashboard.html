{% extends "layout.html" %}

{% block title %}Dashboard - Wallet Service Admin{% endblock %}

{% block content %}
<div class="px-4 py-6 sm:px-0">
    <div class="border-4 border-dashed border-gray-200 rounded-lg p-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Dashboard</h1>
        
        <!-- KPI Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Wallet Float -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold">₹</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Wallet Float</dt>
                                <dd class="text-lg font-medium text-gray-900">₹{{ "%.2f"|format(total_wallet_float) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Users -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold">👥</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ total_users }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Topups (30d) -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold">↗</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Topups (30d)</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ topups_30d }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Withdrawals (30d) -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold">↙</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Withdrawals (30d)</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ withdrawals_30d }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="/admin/users" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <h3 class="font-medium text-gray-900">View Users</h3>
                    <p class="text-sm text-gray-500">Manage user accounts and balances</p>
                </a>
                <a href="/admin/journal" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <h3 class="font-medium text-gray-900">Journal Entries</h3>
                    <p class="text-sm text-gray-500">View transaction ledger</p>
                </a>
                <a href="/admin/balances" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <h3 class="font-medium text-gray-900">Balance Overview</h3>
                    <p class="text-sm text-gray-500">Platform and user balances</p>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
