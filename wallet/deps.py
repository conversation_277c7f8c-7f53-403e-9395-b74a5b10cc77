from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Annotated, Optional, Dict, Any
from db import get_db
from settings import settings
from services.auth_service import verify_token
from middleware.auth_middleware import get_current_user, get_current_user_optional


class AuthenticationRequired(HTTPException):
    """Custom exception for authentication required"""
    def __init__(self):
        super().__init__(
            status_code=401,
            detail="Authentication required"
        )


async def get_admin_user(
    request: Request,
    x_api_key: Annotated[str, Header()] = None,
    session_token: Annotated[str, <PERSON><PERSON>()] = None
):
    """Dependency to verify admin API key or session token"""
    # Check API key first (for API access)
    if x_api_key and x_api_key == settings.admin_api_key:
        return {"role": "admin", "auth_type": "api_key"}
    
    # Check session token (for web UI access)
    if session_token:
        user_data = verify_token(session_token)
        if user_data:
            return {"role": "admin", "auth_type": "session", "username": user_data["username"]}
    
    raise AuthenticationRequired()


async def get_admin_user_optional(
    request: Request,
    x_api_key: Annotated[str, Header()] = None,
    session_token: Annotated[str, Cookie()] = None
) -> Optional[dict]:
    """Optional admin authentication - returns None if not authenticated"""
    try:
        return await get_admin_user(request, x_api_key, session_token)
    except HTTPException:
        return None


# Type aliases for dependency injection
DatabaseSession = Annotated[AsyncSession, Depends(get_db)]
AuthenticatedUser = Annotated[Dict[str, Any], Depends(get_current_user)]
OptionalAuthenticatedUser = Annotated[Optional[Dict[str, Any]], Depends(get_current_user_optional)]
AdminUser = Annotated[dict, Depends(get_admin_user)]
OptionalAdminUser = Annotated[Optional[dict], Depends(get_admin_user_optional)]
