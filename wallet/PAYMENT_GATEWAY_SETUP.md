# Payment Gateway Configuration Guide

This guide covers the specific configurations required in Razorpay and Cashfree dashboards to integrate with the wallet service.

---

## 🔵 Razorpay Configuration

### 1. Account Setup

1. **Create Razorpay Account**: Sign up at https://razorpay.com/
2. **Business Verification**: Complete KYC and business verification
3. **Test Mode**: Start with test mode for development

### 2. API Keys Configuration

#### Get API Keys
1. Go to **Settings** → **API Keys**
2. Generate **Key ID** and **Key Secret**
3. Copy both values to your `.env` file:

```bash
RAZORPAY_KEY_ID=rzp_test_1234567890
RAZORPAY_KEY_SECRET=your_secret_key_here
```

### 3. Webhook Configuration

#### Create Webhook Endpoint
1. Go to **Settings** → **Webhooks**
2. Click **Add New Webhook**
3. Configure webhook:

```
Webhook URL: https://yourdomain.com/webhooks/razorpay
Active Events:
  ✅ payment.captured
  ✅ payment.failed
  ✅ order.paid
  ✅ virtual_account.credited
  ✅ virtual_account.closed
```

#### Webhook Secret
1. After creating webhook, copy the **Webhook Secret**
2. Add to `.env` file:

```bash
RAZORPAY_WEBHOOK_SECRET=whsec_1234567890abcdef
```

### 4. Virtual Accounts Setup (Optional)

For auto-collect functionality:

1. Go to **Products** → **Smart Collect**
2. Enable **Virtual Accounts**
3. Configure:
   - **Account Prefix**: Choose a prefix for virtual accounts
   - **Expiry**: Set expiry time (or keep unlimited)
   - **Customer Identification**: Enable for user mapping

### 5. Payment Methods Configuration

1. Go to **Settings** → **Configuration**
2. Enable required payment methods:
   - **Cards**: Visa, Mastercard, RuPay
   - **Net Banking**: Major banks
   - **UPI**: All UPI apps
   - **Wallets**: Paytm, PhonePe, etc.

### 6. Settlement Configuration

1. Go to **Settings** → **Settlements**
2. Configure:
   - **Settlement Schedule**: T+2 or T+3
   - **Bank Account**: Add verified bank account
   - **Auto Settlement**: Enable for automatic transfers

---

## 🟠 Cashfree Configuration

### 1. Account Setup

1. **Create Cashfree Account**: Sign up at https://cashfree.com/
2. **Business Verification**: Complete KYC and business verification
3. **Payout Product**: Enable Cashfree Payouts

### 2. API Credentials

#### Get API Credentials
1. Go to **Developers** → **Credentials**
2. Copy **Client ID** and **Client Secret**
3. Add to `.env` file:

```bash
CASHFREE_CLIENT_ID=your_client_id_here
CASHFREE_CLIENT_SECRET=your_client_secret_here
```

### 3. Webhook Configuration

#### Create Webhook
1. Go to **Developers** → **Webhooks**
2. Click **Add Webhook**
3. Configure webhook:

```
Webhook URL: https://yourdomain.com/webhooks/cashfree
Events:
  ✅ TRANSFER_SUCCESS
  ✅ TRANSFER_FAILED
  ✅ TRANSFER_REVERSED
  ✅ LOW_BALANCE_ALERT
```

#### Webhook Secret
1. Generate or set a **Webhook Secret**
2. Add to `.env` file:

```bash
CASHFREE_WEBHOOK_SECRET=your_webhook_secret_here
```

### 4. Payout Configuration

#### Bank Account Setup
1. Go to **Payouts** → **Settings** → **Bank Accounts**
2. Add your business bank account
3. Complete bank verification process

#### Beneficiary Management
1. Go to **Payouts** → **Beneficiaries**
2. Configure beneficiary validation rules
3. Enable **Auto Beneficiary Addition** if needed

### 5. Cashgram Configuration

For instant payouts to non-bank accounts:

1. Go to **Products** → **Cashgram**
2. Enable Cashgram service
3. Configure:
   - **SMS Templates**: Customize SMS notifications
   - **Validity Period**: Set cashgram validity (default: 3 days)
   - **Auto Expiry**: Enable auto-expiry handling

### 6. Balance and Limits

1. **Add Balance**: Fund your Cashfree wallet
2. **Set Limits**: Configure daily/monthly payout limits
3. **Approval Workflow**: Set up approval process if required

---

## 🔧 Environment Configuration

### Complete .env File Template

```bash
# Database
DB_URL=postgresql+asyncpg://wallet_user:wallet_pass@localhost:5432/wallet

# Razorpay Configuration
RAZORPAY_KEY_ID=rzp_test_1234567890
RAZORPAY_KEY_SECRET=your_razorpay_secret_key
RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret
RAZORPAY_BASE=https://api.razorpay.com/v1

# Cashfree Configuration
CASHFREE_CLIENT_ID=your_cashfree_client_id
CASHFREE_CLIENT_SECRET=your_cashfree_client_secret
CASHFREE_WEBHOOK_SECRET=your_cashfree_webhook_secret
CASHFREE_BASE=https://payout-api.cashfree.com

# Admin Authentication
ADMIN_API_KEY=supersecretadminkey

# Security
SERVICE_IDEMPOTENCY_SALT=your_random_salt_string

# Development
DEBUG=false
LOG_LEVEL=INFO
```

---

## 🧪 Testing Configuration

### Razorpay Test Mode

#### Test Cards
```
Successful Payment:
Card Number: 4111 1111 1111 1111
CVV: 123
Expiry: Any future date

Failed Payment:
Card Number: 4000 0000 0000 0002
CVV: 123
Expiry: Any future date
```

#### Test UPI
```
Successful: success@razorpay
Failed: failure@razorpay
```

### Cashfree Test Mode

#### Test Bank Accounts
```
Successful Transfer:
Account Number: ***************
IFSC: YESB0000262

Failed Transfer:
Account Number: ***************
IFSC: YESB0000262
```

---

## 🔒 Security Best Practices

### API Key Security
1. **Never commit** API keys to version control
2. **Use environment variables** for all credentials
3. **Rotate keys** regularly in production
4. **Restrict API access** by IP if possible

### Webhook Security
1. **Always verify** webhook signatures
2. **Use HTTPS** for webhook URLs
3. **Implement idempotency** for webhook processing
4. **Log all webhook** events for audit

### Production Checklist
- [ ] Switch to production API keys
- [ ] Update webhook URLs to production domain
- [ ] Enable HTTPS for all endpoints
- [ ] Set up monitoring and alerting
- [ ] Configure proper logging
- [ ] Test all payment flows thoroughly

---

## 🚨 Common Issues & Solutions

### Razorpay Issues

#### Webhook Not Receiving
```
Solution:
1. Check webhook URL is publicly accessible
2. Verify HTTPS is enabled
3. Check webhook signature verification
4. Ensure correct events are selected
```

#### Payment Capture Failing
```
Solution:
1. Verify API keys are correct
2. Check payment is in 'authorized' status
3. Ensure sufficient balance in test mode
4. Verify order amount matches payment amount
```

### Cashfree Issues

#### Payout Failing
```
Solution:
1. Check wallet balance is sufficient
2. Verify beneficiary details are correct
3. Ensure bank account is active
4. Check daily/monthly limits
```

#### Webhook Signature Mismatch
```
Solution:
1. Verify webhook secret is correct
2. Check signature generation algorithm
3. Ensure raw body is used for signature
4. Verify timestamp handling
```

---

## 📞 Support Contacts

### Razorpay Support
- **Email**: <EMAIL>
- **Phone**: +91-80-********
- **Documentation**: https://razorpay.com/docs/

### Cashfree Support
- **Email**: <EMAIL>
- **Phone**: +91-80-********
- **Documentation**: https://docs.cashfree.com/

---

*This configuration guide ensures proper integration between your wallet service and both payment gateways.*
